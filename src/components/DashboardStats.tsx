import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { TrendingUp, Target, Award, DollarSign } from "lucide-react";
import { cn } from "@/lib/utils";

interface StatCardProps {
  title: string;
  value: string;
  change: string;
  icon: React.ReactNode;
  trend: 'up' | 'down' | 'neutral';
}

const StatCard = ({ title, value, change, icon, trend }: StatCardProps) => (
  <Card className="p-6 bg-gradient-card border border-border/50 hover:shadow-lg hover:scale-105 hover:border-primary/30 transition-all duration-300 cursor-pointer group">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-muted-foreground">{title}</p>
        <p className="text-2xl font-bold text-foreground">{value}</p>
        <p className={cn(
          "text-xs flex items-center mt-1",
          trend === 'up' ? 'text-success' : 
          trend === 'down' ? 'text-destructive' : 'text-muted-foreground'
        )}>
          <TrendingUp className={cn("w-3 h-3 mr-1", 
            trend === 'down' && 'rotate-180'
          )} />
          {change}
        </p>
      </div>
      <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 group-hover:scale-110 transition-all duration-300">
        {icon}
      </div>
    </div>
  </Card>
);

const StatCardSkeleton = () => (
  <Card className="p-6 bg-gradient-card border border-border/50">
    <div className="flex items-center justify-between">
      <div className="flex-1">
        <Skeleton className="h-4 w-24 mb-2" />
        <Skeleton className="h-8 w-16 mb-2" />
        <Skeleton className="h-3 w-32" />
      </div>
      <Skeleton className="w-12 h-12 rounded-lg" />
    </div>
  </Card>
);

interface DashboardStatsProps {
  loading?: boolean;
}

export const DashboardStats = ({ loading = false }: DashboardStatsProps) => {
  const stats = [
    {
      title: "Today's Predictions", 
      value: "47",
      change: "+12 from yesterday",
      icon: <Target className="w-6 h-6 text-primary" />,
      trend: 'up' as const
    },
    {
      title: "Win Rate (7d)",
      value: "68.2%", 
      change: "+2.1% vs last week",
      icon: <Award className="w-6 h-6 text-success" />,
      trend: 'up' as const
    },
    {
      title: "Avg Confidence",
      value: "74.5%",
      change: "+1.2% improvement", 
      icon: <TrendingUp className="w-6 h-6 text-info" />,
      trend: 'up' as const
    },
    {
      title: "ROI (30d)",
      value: "+12.8%",
      change: "Beating market avg",
      icon: <DollarSign className="w-6 h-6 text-warning" />, 
      trend: 'up' as const
    }
  ];

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {[1, 2, 3, 4].map((i) => (
          <StatCardSkeleton key={i} />
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {stats.map((stat, index) => (
        <StatCard key={index} {...stat} />
      ))}
    </div>
  );
};