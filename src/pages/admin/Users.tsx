import React, { useState, useEffect } from 'react';
import { Header } from '@/components/Header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { apiService as api, User } from '@/services/api';
import { useToast } from '@/hooks/use-toast';
import { Users, UserPlus, UserCheck, UserX, Search, RefreshCw, Edit, Trash2 } from 'lucide-react';

interface UserStats {
  totalUsers: number;
  activeUsers: number;
  premiumUsers: number;
  recentUsers: number;
}

const AdminUsers: React.FC = () => {
  const { toast } = useToast();
  const [users, setUsers] = useState<User[]>([]);
  const [stats, setStats] = useState<UserStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  const fetchUsers = async () => {
    try {
      setLoading(true);

      // Fetch users and stats in parallel
      const [usersResponse, statsResponse] = await Promise.all([
        api.getUsers?.({ page, limit: 20, role: roleFilter !== 'all' ? roleFilter : undefined }) || Promise.resolve({ success: false }),
        api.getUserStats()
      ]);

      if (usersResponse.success && usersResponse.data) {
        setUsers(usersResponse.data.users || []);
        setTotalPages(usersResponse.data.pagination?.pages || 1);
      }

      if (statsResponse.success && statsResponse.data) {
        setStats(statsResponse.data);
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: "Error",
        description: "Failed to load users",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [page, roleFilter]);

  const filteredUsers = users.filter(user =>
    searchQuery === '' ||
    user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    user.fullName?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getRoleBadge = (role: string) => {
    const variants = {
      'SUPER_ADMIN': 'destructive',
      'ADMIN': 'default',
      'USER': 'secondary'
    } as const;

    return (
      <Badge variant={variants[role as keyof typeof variants] || 'secondary'}>
        {role.replace('_', ' ')}
      </Badge>
    );
  };

  const getSubscriptionBadge = (tier: string, status?: string) => {
    const isActive = status === 'ACTIVE';
    const variants = {
      'ENTERPRISE': 'default',
      'PREMIUM': 'default',
      'BASIC': 'secondary',
      'FREE': 'outline'
    } as const;

    return (
      <Badge
        variant={variants[tier as keyof typeof variants] || 'outline'}
        className={!isActive ? 'opacity-50' : ''}
      >
        {tier} {!isActive && status ? `(${status})` : ''}
      </Badge>
    );
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <div className="container mx-auto px-4 py-8">
        <div className="mb-8 flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-2">User Management</h1>
            <p className="text-muted-foreground">Manage user accounts, subscriptions, and permissions</p>
          </div>
          <Button onClick={fetchUsers} disabled={loading} variant="outline" size="sm">
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* User Statistics */}
        <div className="grid gap-6 mb-8">
          <Card className="border-border shadow-carved-deep bg-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                User Statistics
              </CardTitle>
            </CardHeader>
            <CardContent>
              {loading || !stats ? (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="text-center p-4 border border-border rounded-lg shadow-carved bg-card">
                      <div className="h-8 bg-muted animate-pulse rounded w-16 mx-auto mb-2"></div>
                      <div className="h-4 bg-muted animate-pulse rounded w-20 mx-auto"></div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <div className="text-center p-4 border border-border rounded-lg shadow-carved bg-card">
                    <div className="text-2xl font-bold text-primary">{stats.totalUsers.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">Total Users</div>
                  </div>
                  <div className="text-center p-4 border border-border rounded-lg shadow-carved bg-card">
                    <div className="text-2xl font-bold text-success">{stats.activeUsers.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">Active Users</div>
                  </div>
                  <div className="text-center p-4 border border-border rounded-lg shadow-carved bg-card">
                    <div className="text-2xl font-bold text-warning">{stats.premiumUsers.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">Premium Users</div>
                  </div>
                  <div className="text-center p-4 border border-border rounded-lg shadow-carved bg-card">
                    <div className="text-2xl font-bold text-info">{stats.recentUsers.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">New This Week</div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* User Management */}
          <Card className="border-border shadow-carved-deep bg-card">
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>User Management</CardTitle>
                  <CardDescription>Manage user accounts and permissions</CardDescription>
                </div>
                <Button className="flex items-center gap-2">
                  <UserPlus className="h-4 w-4" />
                  Create New User
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {/* Filters */}
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="flex items-center space-x-2 bg-muted/50 rounded-lg px-3 py-2 flex-1">
                  <Search className="w-4 h-4 text-muted-foreground" />
                  <Input
                    placeholder="Search users..."
                    className="border-0 bg-transparent text-sm focus-visible:ring-0 p-0"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>

                <Select value={roleFilter} onValueChange={setRoleFilter}>
                  <SelectTrigger className="w-[150px]">
                    <SelectValue placeholder="Filter by role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Roles</SelectItem>
                    <SelectItem value="USER">User</SelectItem>
                    <SelectItem value="ADMIN">Admin</SelectItem>
                    <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Users Table */}
              <div className="border rounded-lg">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Subscription</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Last Login</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {loading ? (
                      [...Array(5)].map((_, i) => (
                        <TableRow key={i}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <div className="h-8 w-8 bg-muted animate-pulse rounded-full"></div>
                              <div>
                                <div className="h-4 bg-muted animate-pulse rounded w-32 mb-1"></div>
                                <div className="h-3 bg-muted animate-pulse rounded w-24"></div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell><div className="h-6 bg-muted animate-pulse rounded w-16"></div></TableCell>
                          <TableCell><div className="h-6 bg-muted animate-pulse rounded w-20"></div></TableCell>
                          <TableCell><div className="h-6 bg-muted animate-pulse rounded w-16"></div></TableCell>
                          <TableCell><div className="h-4 bg-muted animate-pulse rounded w-20"></div></TableCell>
                          <TableCell><div className="h-8 bg-muted animate-pulse rounded w-16"></div></TableCell>
                        </TableRow>
                      ))
                    ) : filteredUsers.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                          No users found
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredUsers.map((user) => (
                        <TableRow key={user.id}>
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <div className="h-8 w-8 bg-primary/10 rounded-full flex items-center justify-center">
                                <span className="text-sm font-medium text-primary">
                                  {user.fullName?.charAt(0) || user.email.charAt(0).toUpperCase()}
                                </span>
                              </div>
                              <div>
                                <div className="font-medium">{user.fullName || 'No name'}</div>
                                <div className="text-sm text-muted-foreground">{user.email}</div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>{getRoleBadge(user.role)}</TableCell>
                          <TableCell>{getSubscriptionBadge(user.subscriptionTier, user.subscriptionStatus)}</TableCell>
                          <TableCell>
                            <Badge variant={user.emailVerified ? 'default' : 'secondary'}>
                              {user.emailVerified ? 'Verified' : 'Unverified'}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-sm text-muted-foreground">
                            Recently
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Button variant="ghost" size="sm">
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="sm" className="text-destructive hover:text-destructive">
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center items-center space-x-2 mt-6">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(p => Math.max(1, p - 1))}
                    disabled={page === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm text-muted-foreground">
                    Page {page} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                    disabled={page === totalPages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default AdminUsers;