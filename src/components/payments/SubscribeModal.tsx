import React, { useMemo, useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/contexts/AuthContext';
import { apiService } from '@/services/api';
import { Loader2 } from 'lucide-react';
import { toast } from 'sonner';

export interface PlanDef {
  name: string;
  tier: 'basic' | 'premium' | 'enterprise';
  priceUSD: number; // monthly
}

interface Props {
  open: boolean;
  onOpenChange: (v: boolean) => void;
  plan: PlanDef | null;
}

const USD_TO_NGN = 1500; // placeholder conversion; consider moving to settings

export const SubscribeModal: React.FC<Props> = ({ open, onOpenChange, plan }) => {
  const { user, profile } = useAuth();
  const [provider, setProvider] = useState<'flutterwave' | 'paystack'>('flutterwave');
  const [currency, setCurrency] = useState<'USD' | 'NGN'>('USD');
  const [loading, setLoading] = useState(false);

  const amountDisplay = useMemo(() => {
    if (!plan) return '';
    if (currency === 'USD') return `$${plan.priceUSD.toFixed(2)}`;
    return `₦${(plan.priceUSD * USD_TO_NGN).toLocaleString()}`;
  }, [plan, currency]);

  const handleSubscribe = async () => {
    if (!user || !plan) {
      toast.error('You must be logged in');
      return;
    }
    try {
      setLoading(true);
      if (provider === 'paystack') {
        if (currency !== 'NGN') {
          toast.error('Paystack supports NGN only in this setup');
          return;
        }
        const amountNaira = Math.round(plan.priceUSD * USD_TO_NGN);
        const amountKobo = amountNaira * 100; // Paystack expects kobo
        const resp = await apiService.createPaystackPayment({
          amount: amountKobo,
          email: user.email,
          plan: plan.tier,
          currency: 'NGN',
        });
        if (!resp.success) throw new Error(resp.error || 'Paystack error');
        const url = resp.data?.authorization_url;
        if (!url) throw new Error('No authorization URL');
        window.open(url, '_blank');
      } else {
        const amount = currency === 'USD' ? plan.priceUSD : Math.round(plan.priceUSD * USD_TO_NGN);
        const resp = await apiService.createFlutterwavePayment({
          amount,
          currency: currency,
          plan: plan.tier,
          customer: {
              email: user.email,
              name: profile?.full_name || user.email,
            },
        });
        if (!resp.success) throw new Error(resp.error || 'Flutterwave error');
        const url = resp.data?.payment_link;
        if (!url) throw new Error('No payment link');
        window.open(url, '_blank');
      }
      toast.success('Redirecting to payment');
      onOpenChange(false);
    } catch (e) {
      toast.error(e instanceof Error ? e.message : 'Payment initiation failed');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Subscribe {plan ? `- ${plan.name}` : ''}</DialogTitle>
        </DialogHeader>
        {!plan ? (
          <div className="text-sm text-muted-foreground">No plan selected.</div>
        ) : (
          <div className="space-y-4">
            <div className="text-sm">You will be redirected to a secure checkout. Plan price: <strong>{amountDisplay}</strong> / month.</div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Provider</Label>
                <Select value={provider} onValueChange={(v) => setProvider(v as any)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="flutterwave">Flutterwave</SelectItem>
                    <SelectItem value="paystack">Paystack (NGN)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label>Currency</Label>
                <Select value={currency} onValueChange={(v) => setCurrency(v as any)}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="NGN">NGN</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <Button disabled={loading} onClick={handleSubscribe} className="w-full">
              {loading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
              Proceed to payment
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
