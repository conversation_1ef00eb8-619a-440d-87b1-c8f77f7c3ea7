import React, { useState, useEffect } from 'react';
import { Head<PERSON> } from '@/components/Header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Target, Plus, Edit, Trash2, Search, Filter, Upload, Play, RefreshCw, Brain, Eye, EyeOff } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiService as api, PredictionData, SportsData } from '@/services/api';

const AdminPredictions: React.FC = () => {
  const { toast } = useToast();
  const [predictions, setPredictions] = useState<PredictionData[]>([]);
  const [sportsData, setSportsData] = useState<SportsData[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [sportFilter, setSportFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Create/Edit prediction state
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isGenerateDialogOpen, setIsGenerateDialogOpen] = useState(false);
  const [editingPrediction, setEditingPrediction] = useState<PredictionData | null>(null);
  const [selectedMatch, setSelectedMatch] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form state for manual prediction creation
  const [formData, setFormData] = useState({
    sport: '',
    league: '',
    homeTeam: '',
    awayTeam: '',
    matchDate: '',
    market: '',
    pick: '',
    confidence: 70,
    odds: 0,
    analysis: '',
    isPremium: false
  });

  const fetchPredictions = async () => {
    try {
      setLoading(true);
      const response = await api.getAllPredictions({
        page,
        limit: 20,
        sport: sportFilter !== 'all' ? sportFilter : undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined
      });

      if (response.success && response.data) {
        setPredictions(response.data.predictions);
        setTotalPages(response.data.pagination?.pages || 1);
      }
    } catch (error) {
      console.error('Error fetching predictions:', error);
      toast({
        title: "Error",
        description: "Failed to load predictions",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const fetchSportsData = async () => {
    try {
      const response = await api.getSportsData({
        status: 'UPCOMING',
        limit: 50
      });

      if (response.success && response.data) {
        setSportsData(response.data.matches);
      }
    } catch (error) {
      console.error('Error fetching sports data:', error);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await Promise.all([fetchPredictions(), fetchSportsData()]);
  };

  const handleCreatePrediction = async () => {
    try {
      setIsSubmitting(true);
      const response = await api.createPrediction(formData);

      if (response.success) {
        toast({
          title: "Success",
          description: "Prediction created successfully",
        });
        setIsCreateDialogOpen(false);
        resetForm();
        await fetchPredictions();
      } else {
        throw new Error(response.error);
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create prediction",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGeneratePredictions = async () => {
    try {
      setIsSubmitting(true);
      const response = await api.generatePredictions(selectedMatch);

      if (response.success) {
        toast({
          title: "Success",
          description: `Generated ${response.data?.length || 0} predictions`,
        });
        setIsGenerateDialogOpen(false);
        setSelectedMatch('');
        await fetchPredictions();
      } else {
        throw new Error(response.error);
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to generate predictions",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setFormData({
      sport: '',
      league: '',
      homeTeam: '',
      awayTeam: '',
      matchDate: '',
      market: '',
      pick: '',
      confidence: 70,
      odds: 0,
      analysis: '',
      isPremium: false
    });
  };

  useEffect(() => {
    fetchPredictions();
    fetchSportsData();
  }, [page, sportFilter, statusFilter]);

  const filteredPredictions = predictions.filter(prediction => {
    const matchesSearch = searchQuery === '' ||
      prediction.homeTeam.toLowerCase().includes(searchQuery.toLowerCase()) ||
      prediction.awayTeam.toLowerCase().includes(searchQuery.toLowerCase()) ||
      prediction.league.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesSearch;
  });

  return (
    <div className="min-h-screen bg-background">
      <Header />

      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold mb-2">Prediction Management</h1>
            <p className="text-muted-foreground">Create and manage AI predictions</p>
          </div>
          <div className="flex gap-4">
            <Button
              onClick={handleRefresh}
              disabled={refreshing}
              variant="outline"
              size="sm"
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Dialog open={isGenerateDialogOpen} onOpenChange={setIsGenerateDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <Brain className="h-4 w-4 mr-2" />
                  Generate AI Predictions
                </Button>
              </DialogTrigger>
            </Dialog>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="bg-gradient-primary">
                  <Plus className="h-4 w-4 mr-2" />
                  New Prediction
                </Button>
              </DialogTrigger>
            </Dialog>
          </div>
        </div>

        {/* Predictions Management */}
        <Card className="border-border shadow-carved-deep bg-card">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Predictions Management
            </CardTitle>
            <CardDescription>Manage all predictions and their status</CardDescription>
          </CardHeader>
          <CardContent>
            {/* Filters */}
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="flex items-center space-x-2 bg-muted/50 rounded-lg px-3 py-2 flex-1">
                <Search className="w-4 h-4 text-muted-foreground" />
                <Input
                  placeholder="Search predictions..."
                  className="border-0 bg-transparent text-sm focus-visible:ring-0 p-0"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <Select value={sportFilter} onValueChange={setSportFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filter by sport" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Sports</SelectItem>
                  <SelectItem value="football">Football</SelectItem>
                  <SelectItem value="basketball">Basketball</SelectItem>
                  <SelectItem value="tennis">Tennis</SelectItem>
                  <SelectItem value="baseball">Baseball</SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="WIN">Won</SelectItem>
                  <SelectItem value="LOSS">Lost</SelectItem>
                  <SelectItem value="VOID">Void</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Predictions Table */}
            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Match</TableHead>
                    <TableHead>Market</TableHead>
                    <TableHead>Pick</TableHead>
                    <TableHead>Confidence</TableHead>
                    <TableHead>Odds</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Premium</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    [...Array(5)].map((_, i) => (
                      <TableRow key={i}>
                        <TableCell>
                          <div className="space-y-2">
                            <div className="h-4 bg-muted animate-pulse rounded w-32"></div>
                            <div className="h-3 bg-muted animate-pulse rounded w-24"></div>
                          </div>
                        </TableCell>
                        <TableCell><div className="h-4 bg-muted animate-pulse rounded w-20"></div></TableCell>
                        <TableCell><div className="h-4 bg-muted animate-pulse rounded w-24"></div></TableCell>
                        <TableCell><div className="h-4 bg-muted animate-pulse rounded w-12"></div></TableCell>
                        <TableCell><div className="h-4 bg-muted animate-pulse rounded w-16"></div></TableCell>
                        <TableCell><div className="h-6 bg-muted animate-pulse rounded w-16"></div></TableCell>
                        <TableCell><div className="h-6 bg-muted animate-pulse rounded w-12"></div></TableCell>
                        <TableCell><div className="h-8 bg-muted animate-pulse rounded w-20"></div></TableCell>
                      </TableRow>
                    ))
                  ) : filteredPredictions.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8 text-muted-foreground">
                        No predictions found
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredPredictions.map((prediction) => (
                      <TableRow key={prediction.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">
                              {prediction.homeTeam} vs {prediction.awayTeam}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {prediction.league} • {new Date(prediction.matchDate).toLocaleDateString()}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="text-sm">{prediction.market}</TableCell>
                        <TableCell className="font-medium">{prediction.pick}</TableCell>
                        <TableCell>
                          <Badge variant="outline" className="text-xs">
                            {prediction.confidence}%
                          </Badge>
                        </TableCell>
                        <TableCell className="font-mono text-sm">
                          {prediction.odds > 0 ? '+' : ''}{prediction.odds}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              prediction.status === 'WIN' ? 'default' :
                              prediction.status === 'LOSS' ? 'destructive' :
                              'secondary'
                            }
                          >
                            {prediction.status || 'Pending'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handlePublishPrediction(prediction.id, !prediction.isPremium)}
                          >
                            {prediction.isPremium ? (
                              <Eye className="h-4 w-4 text-warning" />
                            ) : (
                              <EyeOff className="h-4 w-4 text-muted-foreground" />
                            )}
                          </Button>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => {
                                setEditingPrediction(prediction);
                                setFormData({
                                  sport: prediction.sport,
                                  league: prediction.league,
                                  homeTeam: prediction.homeTeam,
                                  awayTeam: prediction.awayTeam,
                                  matchDate: prediction.matchDate,
                                  market: prediction.market,
                                  pick: prediction.pick,
                                  confidence: prediction.confidence,
                                  odds: prediction.odds,
                                  analysis: prediction.analysis || '',
                                  isPremium: prediction.isPremium || false
                                });
                                setIsCreateDialogOpen(true);
                              }}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-destructive hover:text-destructive"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center space-x-2 mt-6">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(p => Math.max(1, p - 1))}
                  disabled={page === 1}
                >
                  Previous
                </Button>
                <span className="text-sm text-muted-foreground">
                  Page {page} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                  disabled={page === totalPages}
                >
                  Next
                </Button>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Create/Edit Prediction Dialog */}
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingPrediction ? 'Edit Prediction' : 'Create New Prediction'}
            </DialogTitle>
            <DialogDescription>
              {editingPrediction ? 'Update the prediction details' : 'Create a new prediction manually'}
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="sport">Sport</Label>
              <Select value={formData.sport} onValueChange={(value) => setFormData(prev => ({ ...prev, sport: value }))}>
                <SelectTrigger>
                  <SelectValue placeholder="Select sport" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="football">Football</SelectItem>
                  <SelectItem value="basketball">Basketball</SelectItem>
                  <SelectItem value="tennis">Tennis</SelectItem>
                  <SelectItem value="baseball">Baseball</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="league">League</Label>
              <Input
                id="league"
                value={formData.league}
                onChange={(e) => setFormData(prev => ({ ...prev, league: e.target.value }))}
                placeholder="e.g., Premier League"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="homeTeam">Home Team</Label>
              <Input
                id="homeTeam"
                value={formData.homeTeam}
                onChange={(e) => setFormData(prev => ({ ...prev, homeTeam: e.target.value }))}
                placeholder="Home team name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="awayTeam">Away Team</Label>
              <Input
                id="awayTeam"
                value={formData.awayTeam}
                onChange={(e) => setFormData(prev => ({ ...prev, awayTeam: e.target.value }))}
                placeholder="Away team name"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="matchDate">Match Date</Label>
              <Input
                id="matchDate"
                type="datetime-local"
                value={formData.matchDate}
                onChange={(e) => setFormData(prev => ({ ...prev, matchDate: e.target.value }))}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="market">Market</Label>
              <Input
                id="market"
                value={formData.market}
                onChange={(e) => setFormData(prev => ({ ...prev, market: e.target.value }))}
                placeholder="e.g., Moneyline, Over/Under"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="pick">Pick</Label>
              <Input
                id="pick"
                value={formData.pick}
                onChange={(e) => setFormData(prev => ({ ...prev, pick: e.target.value }))}
                placeholder="e.g., Home Win, Over 2.5"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="odds">Odds</Label>
              <Input
                id="odds"
                type="number"
                step="0.01"
                value={formData.odds}
                onChange={(e) => setFormData(prev => ({ ...prev, odds: parseFloat(e.target.value) || 0 }))}
                placeholder="e.g., 1.85, -110"
              />
            </div>

            <div className="space-y-2 col-span-2">
              <Label htmlFor="confidence">Confidence: {formData.confidence}%</Label>
              <Input
                id="confidence"
                type="range"
                min="1"
                max="100"
                value={formData.confidence}
                onChange={(e) => setFormData(prev => ({ ...prev, confidence: parseInt(e.target.value) }))}
                className="w-full"
              />
            </div>

            <div className="space-y-2 col-span-2">
              <Label htmlFor="analysis">Analysis (Optional)</Label>
              <Textarea
                id="analysis"
                value={formData.analysis}
                onChange={(e) => setFormData(prev => ({ ...prev, analysis: e.target.value }))}
                placeholder="Prediction analysis and reasoning..."
                rows={3}
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsCreateDialogOpen(false);
              setEditingPrediction(null);
              resetForm();
            }}>
              Cancel
            </Button>
            <Button onClick={handleCreatePrediction} disabled={isSubmitting}>
              {isSubmitting ? 'Saving...' : editingPrediction ? 'Update' : 'Create'} Prediction
            </Button>
          </DialogFooter>
        </DialogContent>

        {/* Generate AI Predictions Dialog */}
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Generate AI Predictions</DialogTitle>
            <DialogDescription>
              Select a match to generate AI-powered predictions
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="selectedMatch">Select Match</Label>
              <Select value={selectedMatch} onValueChange={setSelectedMatch}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a match" />
                </SelectTrigger>
                <SelectContent>
                  {sportsData.map((match) => (
                    <SelectItem key={match.id} value={match.id}>
                      {match.homeTeam} vs {match.awayTeam} - {match.league}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsGenerateDialogOpen(false);
              setSelectedMatch('');
            }}>
              Cancel
            </Button>
            <Button onClick={handleGeneratePredictions} disabled={isSubmitting || !selectedMatch}>
              {isSubmitting ? 'Generating...' : 'Generate Predictions'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </div>
    </div>
  );
};

export default AdminPredictions;