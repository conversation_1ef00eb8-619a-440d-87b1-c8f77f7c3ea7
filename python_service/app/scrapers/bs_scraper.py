from typing import Dict, Any, Optional
import requests
from bs4 import BeautifulSoup
from app.scrapers.base import ScrapeRequest, backoff_sleep
from app.scrapers.scraperapi import fetch_via_scraperapi
from app.config import settings


def scrape_static(req: ScrapeRequest) -> Dict[str, Any]:
    headers = req.headers or {"User-Agent": "Mozilla/5.0"}

    proxies = None
    if req.proxy_url:
        proxies = {
            "http": req.proxy_url,
            "https": req.proxy_url,
        }

    last_exc: Optional[Exception] = None
    for attempt in range(4):
        try:
            resp = requests.get(
                req.url,
                headers=headers,
                proxies=proxies,
                timeout=req.timeout,
            )
            resp.raise_for_status()
            html = resp.text
            soup = BeautifulSoup(html, "lxml")

            title = soup.title.text.strip() if soup.title else ""
            description = ""
            desc_tag = soup.find("meta", attrs={"name": "description"})
            if desc_tag and desc_tag.get("content"):
                description = desc_tag["content"].strip()

            return {
                "success": True,
                "data": {
                    "title": title,
                    "description": description,
                    "text": soup.get_text(" ", strip=True)[:2000],
                },
                "metadata": {
                    "url": req.url,
                    "engine": "requests+bs4",
                },
            }
        except Exception as exc:
            last_exc = exc
            if attempt == 3:
                break
            backoff_sleep(attempt)

    # Fallback to ScraperAPI if configured
    if settings.SCRAPERAPI_KEY:
        try:
            resp = fetch_via_scraperapi(req.url)
            html = resp.text
            soup = BeautifulSoup(html, "lxml")
            title = soup.title.text.strip() if soup.title else ""
            description = ""
            desc_tag = soup.find("meta", attrs={"name": "description"})
            if desc_tag and desc_tag.get("content"):
                description = desc_tag["content"].strip()
            return {
                "success": True,
                "data": {
                    "title": title,
                    "description": description,
                    "text": soup.get_text(" ", strip=True)[:2000],
                },
                "metadata": {
                    "url": req.url,
                    "engine": "scraperapi",
                },
            }
        except Exception as exc:
            last_exc = exc

    return {"success": False, "error": str(last_exc) if last_exc else "Unknown error"}
