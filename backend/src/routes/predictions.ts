import { Router } from 'express';
import {
  getAllPredictions,
  getPredictionById,
  generatePredictions,
  createManualPrediction,
  updatePrediction,
  deletePrediction,
  publishPrediction,
  getPredictionStats,
} from '@/controllers/predictionController';
import { authenticate, authorize, optionalAuth } from '@/middleware/auth';
// Removed enum imports - using string literals instead
import { validate, validateQuery } from '@/utils/validation';
import { predictionSchema, paginationSchema, predictionFilterSchema, dateRangeSchema } from '@/utils/validation';

const router = Router();

// Public routes (with optional auth for premium content)
router.get('/', optionalAuth, validateQuery(paginationSchema.concat(predictionFilterSchema).concat(dateRangeSchema)), getAllPredictions);
router.get('/stats', optionalAuth, getPredictionStats);
router.get('/:id', optionalAuth, getPredictionById);

// Protected routes - Authenticated users
router.use(authenticate);

// Admin and user routes
router.post('/manual', validate(predictionSchema), createManualPrediction);
router.put('/:id', updatePrediction);
router.delete('/:id', deletePrediction);

// Admin only routes
router.post('/generate', authorize('ADMIN', 'SUPER_ADMIN'), generatePredictions);
router.put('/:id/publish', authorize('ADMIN', 'SUPER_ADMIN'), publishPrediction);

export default router;
