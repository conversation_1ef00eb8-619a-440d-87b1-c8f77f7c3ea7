import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { NeumorphicCard } from '@/components/ui/neumorphic-card';
import { Lock, TrendingUp, Clock, Zap } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { apiService } from '@/services/api';
import { format } from 'date-fns';
import { useAOS } from '@/hooks/useAOS';
import { useToast } from '@/hooks/use-toast';

interface EnhancedPrediction {
  id: string;
  sport: string;
  league: string;
  home_team: string;
  away_team: string;
  match_date: string;
  market: string;
  pick: string;
  confidence: number;
  odds: number;
  edge?: number | null;
  source: string;
  status: string;
  is_premium?: boolean | null;
}

const EnhancedPredictionsTable: React.FC = () => {
  const [predictions, setPredictions] = useState<EnhancedPrediction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { profile } = useAuth();
  const { ref: containerRef, isVisible } = useAOS({ threshold: 0.1, delay: 200 });
  const { toast } = useToast();
  const seededRef = useRef(false);
  
  // User separation logic - show based on subscription and user role
  const canViewPremium = profile?.subscription_status === 'active' || 
                        profile?.role === 'admin' || 
                        profile?.role === 'super_admin';

  // Real-time subscription for predictions
  useEffect(() => {
    const fetchPredictions = async () => {
      try {
        // Get predictions from API
        const response = await apiService.getAllPredictions();

        let rows: any[] | undefined;
        if (response.success) {
          rows = response.data?.predictions?.slice(0, 20) || [];
        } else {
          console.warn('API failed to get predictions', response.error);
          rows = [];
        }

        // Enhanced duplicate removal with comprehensive unique keys
        const seenKeys = new Set();
        const uniquePredictions = (rows || []).filter((prediction) => {
          const uniqueKey = `${prediction.sport}-${prediction.league}-${prediction.home_team}-${prediction.away_team}-${prediction.market}`;
          if (seenKeys.has(uniqueKey)) return false;
          seenKeys.add(uniqueKey);
          return true;
        });

        setPredictions(uniquePredictions);
        if (uniquePredictions.length === 0 && !seededRef.current) {
          seededRef.current = true;
          try {
            // Seed data via scraper API to populate predictions
            const resp = await apiService.processSportsData({ processAll: true });
            if (!resp.success) {
              console.error('Failed to trigger scraper', resp.error);
            }
          } catch (e) {
            console.error('Failed to trigger scraper', e);
          }
        }
      } catch (err) {
        console.error('Error fetching predictions:', err);
        setError('Failed to load predictions from API');
      } finally {
        setLoading(false);
      }
    };

    fetchPredictions();

    // Set up polling for real-time updates (since we don't have WebSocket yet)
    const interval = setInterval(() => {
      fetchPredictions();
    }, 30000); // Poll every 30 seconds

    return () => {
      clearInterval(interval);
    };
  }, [toast]);

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-success';
    if (confidence >= 60) return 'text-warning';
    return 'text-muted-foreground';
  };

  const getEdgeColor = (edge: number) => {
    if (edge >= 10) return 'text-success';
    if (edge >= 5) return 'text-warning';
    return 'text-muted-foreground';
  };

  if (loading) {
    return (
      <NeumorphicCard className="animate-pulse">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-bruno">
            <TrendingUp className="h-5 w-5" />
            Enhanced Predictions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="neumorphic-inset h-24 rounded-md"></div>
            ))}
          </div>
        </CardContent>
      </NeumorphicCard>
    );
  }

  if (error) {
    return (
      <NeumorphicCard>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-bruno">
            <TrendingUp className="h-5 w-5" />
            Enhanced Predictions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-destructive text-center py-8">{error}</p>
        </CardContent>
      </NeumorphicCard>
    );
  }

  if (predictions.length === 0) {
    return (
      <NeumorphicCard>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-bruno">
            <TrendingUp className="h-5 w-5" />
            Enhanced Predictions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-8 font-bruno">No predictions available at the moment.</p>
        </CardContent>
      </NeumorphicCard>
    );
  }

  return (
    <div ref={containerRef} className={`aos-card relative z-30 ${isVisible ? 'animate' : ''}`}>
      <NeumorphicCard className="shadow-glow" pulse>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-bruno drop-shadow-md">
            <TrendingUp className="h-5 w-5" />
            Enhanced Live Predictions
            <Badge variant="secondary" className="ml-auto neumorphic border-none">
              {predictions.length} Active
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {predictions.map((prediction, index) => {
              const isPremium = prediction.is_premium;
              const canView = canViewPremium || !isPremium;

              return (
                <NeumorphicCard
                  key={`${prediction.id}-${index}`}
                  variant={isPremium ? 'button' : 'default'}
                  className={`transition-all duration-500 ${
                    isPremium ? 'border-warning/30' : ''
                  }`}
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-3 gap-2">
                    <div className="flex flex-wrap items-center gap-2">
                      <Badge variant="outline" className="text-xs font-bruno neumorphic border-none">
                        {prediction.sport}
                      </Badge>
                      <Badge variant="secondary" className="text-xs font-bruno neumorphic border-none">
                        {prediction.league}
                      </Badge>
                      {isPremium && (
                        <Badge className="bg-gradient-primary text-xs font-bruno drop-shadow-sm neumorphic border-none">
                          Premium
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-2 text-xs text-muted-foreground font-bruno shrink-0">
                      <Clock className="h-3 w-3" />
                      <span className="whitespace-nowrap">
                        {format(new Date(prediction.match_date), 'MMM dd, HH:mm')}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-semibold text-sm mb-1 font-bruno drop-shadow-sm">
                        {prediction.home_team} vs {prediction.away_team}
                      </h4>
                      <p className="text-xs text-muted-foreground font-bruno">
                        {prediction.market}
                      </p>
                    </div>

                    {canView ? (
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium font-bruno">Pick:</span>
                          <Badge variant="default" className="font-bruno neumorphic border-none">{prediction.pick}</Badge>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-bruno">Confidence:</span>
                          <span className={`text-sm font-bold font-bruno drop-shadow-sm ${getConfidenceColor(prediction.confidence)}`}>
                            {prediction.confidence}%
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-bruno">Edge:</span>
                          <span className={`text-sm font-bold font-bruno drop-shadow-sm ${getEdgeColor(prediction.edge || 0)}`}>
                            {(prediction.edge || 0) > 0 ? '+' : ''}{(prediction.edge || 0).toFixed(1)}%
                          </span>
                        </div>
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-bruno">Odds:</span>
                          <span className="text-sm font-medium font-bruno">
                            {prediction.odds > 0 ? '+' : ''}{prediction.odds}
                          </span>
                        </div>
                      </div>
                    ) : (
                      <NeumorphicCard variant="inset" className="flex items-center justify-center">
                        <div className="text-center">
                          <Lock className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
                          <p className="text-sm text-muted-foreground mb-2 font-bruno">Premium Prediction</p>
                          <Button size="sm" variant="outline" className="font-bruno neumorphic border-none micro-bounce">
                            Upgrade to View
                          </Button>
                        </div>
                      </NeumorphicCard>
                    )}
                  </div>
                </NeumorphicCard>
              );
            })}
          </div>
        </CardContent>
      </NeumorphicCard>
    </div>
  );
};

export default EnhancedPredictionsTable;