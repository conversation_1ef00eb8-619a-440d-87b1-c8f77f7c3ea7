from typing import Optional, Dict, Any
import requests
from app.config import settings


def fetch_via_scraperapi(url: str, params: Optional[Dict[str, Any]] = None, render: bool = False) -> requests.Response:
    if not settings.SCRAPERAPI_KEY:
        raise RuntimeError("SCRA<PERSON><PERSON><PERSON>I_KEY not set")
    payload = {"api_key": settings.SCRAPERAPI_KEY, "url": url}
    if render:
        payload["render"] = "true"
    if params:
        payload.update(params)
    resp = requests.get("https://api.scraperapi.com/", params=payload, timeout=settings.REQUEST_TIMEOUT_SECONDS)
    resp.raise_for_status()
    return resp
