import React from 'react';
import { Head<PERSON> } from '@/components/Header';
import <PERSON><PERSON><PERSON>ooter from '@/components/MinimalFooter';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { SEOHead } from '@/components/SEOHead';

const AgeVerification: React.FC = () => {
  return (
    <>
      <SEOHead
        title="Age Verification - 1300BLK AI"
        description="Our age verification policy ensures responsible access to sports prediction content. Learn how we verify users are 18+."
        canonical="https://1300blk.online/legal/age-verification"
      />

      <div className="min-h-screen bg-gradient-hero">
        <Header />

        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto">
            <Card className="bg-gradient-card border-red-500">
              <CardHeader className="text-center pb-6">
                <CardTitle className="text-4xl font-bruno tracking-wide text-red-500">Age Verification</CardTitle>
              </CardHeader>
              <CardContent className="prose prose-neutral dark:prose-invert max-w-none space-y-6">
                <section>
                  <h2 className="text-2xl font-semibold mb-3">Minimum Age Requirement</h2>
                  <p className="text-muted-foreground leading-relaxed">
                    Access to our predictions and subscription services is restricted to individuals who are 18 years of age or older, or the legal age of majority in your jurisdiction. By using our platform, you confirm that you meet this requirement.
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold mb-3">Verification Process</h2>
                  <p className="text-muted-foreground leading-relaxed">
                    We may require additional verification steps when we have reason to believe a user may be underage or when mandated by applicable laws. Verification may include government-issued identification and a liveness check. Personal data collected for verification is processed according to our Privacy Policy and retained only as long as necessary.
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold mb-3">Parental Controls</h2>
                  <p className="text-muted-foreground leading-relaxed">
                    If you are a parent or guardian and believe an underage individual has accessed our services, please contact us immediately. We will promptly investigate and take appropriate action.
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold mb-3">Jurisdictional Restrictions</h2>
                  <p className="text-muted-foreground leading-relaxed">
                    Some regions impose additional restrictions on sports-related content. It is your responsibility to ensure access and usage complies with your local laws and regulations.
                  </p>
                </section>
              </CardContent>
            </Card>
          </div>
        </div>

        <MinimalFooter />
      </div>
    </>
  );
};

export default AgeVerification;
