import React, { Suspense, useEffect } from 'react';
import AOS from 'aos';
import 'aos/dist/aos.css';
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import Predictions from "./pages/Predictions";
import Tips from "./pages/Tips";
import Leagues from "./pages/Leagues";
import Categories from "./pages/Categories";
import NotFound from "./pages/NotFound";
import About from "./pages/About";
import FAQ from "./pages/FAQ";
import Contact from "./pages/Contact";
import Terms from "./pages/Terms";
import Privacy from "./pages/Privacy";
import AgeVerification from "./pages/AgeVerification";
import WebScraper from "./pages/WebScraper";

// Auth Pages
import Login from "./pages/auth/Login";
import Signup from "./pages/auth/Signup";
import AdminLogin from "./pages/admin/AdminLogin";
import { ProtectedRoute } from "./components/auth/ProtectedRoute";
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';

// User Pages
import UserDashboard from "./pages/user/Dashboard";
import UserPredictions from "./pages/user/Predictions";
import UserFavorites from "./pages/user/Favorites";
import UserSubscription from "./pages/user/Subscription";
import UserSettings from "./pages/user/Settings";
import UserEarningsCenter from "./pages/user/EarningsCenter";

// Admin Pages
import AdminDashboard from "./pages/admin/Dashboard";
import AdminUsers from "./pages/admin/Users";
import AdminApiKeys from "./pages/admin/ApiKeys";
import AdminAnalytics from "./pages/admin/Analytics";
import AdminSettings from "./pages/admin/Settings";
import AdminGlobalSettings from "./pages/admin/GlobalSettings";
import AdminPredictions from "./pages/admin/Predictions";
import AdminScheduleManager from "./pages/admin/ScheduleManager";
import AdminSubscriptions from "./pages/admin/Subscriptions";
import AdminAds from "./pages/admin/Ads";
import AdminModels from "./pages/admin/Models";
import CreateUsers from "./pages/admin/CreateUsers";
import Unauthorized from "./pages/Unauthorized";
import TestBackend from "./pages/TestBackend";

const queryClient = new QueryClient();

const App = () => {
  console.log('App component rendering...');

  useEffect(() => {
    AOS.init({
      duration: 800,
      easing: 'ease-in-out',
      once: true,
      mirror: false,
      offset: 100,
    });
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider>
        <AuthProvider>
          <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
        <Suspense fallback={<div className="min-h-screen bg-gradient-hero flex items-center justify-center"><div className="text-primary font-orbitron">Loading...</div></div>}>
        <Routes>
          {/* Public routes */}
          <Route path="/" element={<Index />} />
          <Route path="/predictions" element={<Predictions />} />
          <Route path="/categories" element={<Categories />} />
          <Route path="/leagues" element={<Leagues />} />
          <Route path="/tips" element={<Tips />} />
          <Route path="/auth/login" element={<Login />} />
          <Route path="/auth/signup" element={<Signup />} />
          <Route path="/admin/login" element={<AdminLogin />} />
          <Route path="/unauthorized" element={<Unauthorized />} />
          
          {/* New public pages */}
          <Route path="/about" element={<About />} />
          <Route path="/faq" element={<FAQ />} />
          <Route path="/contact" element={<Contact />} />
          <Route path="/terms" element={<Terms />} />
          <Route path="/privacy" element={<Privacy />} />
           <Route path="/legal/age-verification" element={<AgeVerification />} />
          <Route path="/web-scraper" element={<WebScraper />} />

          {/* Protected User routes */}
          <Route 
            path="/user/dashboard" 
            element={
              <ProtectedRoute requireAuth={true}>
                <UserDashboard />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/user/predictions" 
            element={
              <ProtectedRoute requireAuth={true}>
                <UserPredictions />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/user/favorites" 
            element={
              <ProtectedRoute requireAuth={true}>
                <UserFavorites />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/user/settings" 
            element={
              <ProtectedRoute requireAuth={true}>
                <UserSettings />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/user/subscription" 
            element={
              <ProtectedRoute requireAuth={true}>
                <UserSubscription />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/user/earnings" 
            element={
              <ProtectedRoute requireAuth={true}>
                <UserEarningsCenter />
              </ProtectedRoute>
            } 
          />

          {/* Protected Admin routes */}
          <Route 
            path="/admin" 
            element={
              <ProtectedRoute requireAdmin={true}>
                <AdminDashboard />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/admin/dashboard" 
            element={
              <ProtectedRoute requireAdmin={true}>
                <AdminDashboard />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/admin/users" 
            element={
              <ProtectedRoute requireSuperAdmin={true}>
                <AdminUsers />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/admin/create-users" 
            element={
              <ProtectedRoute requireAdmin={true}>
                <CreateUsers />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/admin/predictions" 
            element={
              <ProtectedRoute requireAdmin={true}>
                <AdminPredictions />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/admin/schedule" 
            element={
              <ProtectedRoute requireAdmin={true}>
                <AdminScheduleManager />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/admin/analytics" 
            element={
              <ProtectedRoute requireAdmin={true}>
                <AdminAnalytics />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/admin/settings" 
            element={
              <ProtectedRoute requireAdmin={true}>
                <AdminSettings />
              </ProtectedRoute>
            } 
          />
          <Route 
            path="/admin/global-settings" 
            element={
              <ProtectedRoute requireSuperAdmin={true}>
                <AdminGlobalSettings />
              </ProtectedRoute>
            } 
          />
          <Route
            path="/admin/api-keys"
            element={
              <ProtectedRoute requireSuperAdmin={true}>
                <AdminApiKeys />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/subscriptions"
            element={
              <ProtectedRoute requireAdmin={true}>
                <AdminSubscriptions />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/ads"
            element={
              <ProtectedRoute requireAdmin={true}>
                <AdminAds />
              </ProtectedRoute>
            }
          />
          <Route
            path="/admin/models"
            element={
              <ProtectedRoute requireAdmin={true}>
                <AdminModels />
              </ProtectedRoute>
            }
          />

          {/* Test Backend Route */}
          <Route path="/test-backend" element={<TestBackend />} />

          {/* Catch-all route for 404 */}
          <Route path="*" element={<NotFound />} />
        </Routes>
        </Suspense>
          </BrowserRouter>
          </TooltipProvider>
        </AuthProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
};

export default App;
