import React, { useState } from 'react';
import { Header } from '@/components/Header';
import MinimalFooter from '@/components/MinimalFooter';
import { SEOHead } from '@/components/SEOHead';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { Badge } from '@/components/ui/badge';
import { 
  UserPlus, 
  Shield, 
  Users, 
  Crown, 
  CheckCircle,
  AlertCircle
} from 'lucide-react';

const CreateUsers: React.FC = () => {
  const { toast } = useToast();
  const { createAdminUser, isAdmin, isSuperAdmin } = useAuth();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    fullName: '',
    role: 'user' as 'user' | 'admin' | 'super_admin'
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [createdUsers, setCreatedUsers] = useState<Array<{
    id: string;
    email: string;
    fullName: string;
    role: string;
    createdAt: string;
  }>>([]);

  // Check if user has permission to create admin users
  if (!isAdmin && !isSuperAdmin) {
    return (
      <div className="min-h-screen bg-gradient-hero flex items-center justify-center">
        <Card className="bg-card/80 backdrop-blur-sm border-border/50 p-8 text-center max-w-md">
          <AlertCircle className="w-12 h-12 text-warning mx-auto mb-4" />
          <h2 className="text-2xl font-orbitron font-bold mb-4 text-foreground">
            Access Denied
          </h2>
          <p className="text-muted-foreground font-exo">
            You don't have permission to access this page. Admin privileges required.
          </p>
        </Card>
      </div>
    );
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Validate permissions for role creation
      if (formData.role === 'super_admin' && !isSuperAdmin) {
        toast({
          title: "Permission Denied",
          description: "Only super admins can create other super admins.",
          variant: "destructive"
        });
        return;
      }

      if (formData.role === 'admin' && !isSuperAdmin) {
        toast({
          title: "Permission Denied", 
          description: "Only super admins can create admin users.",
          variant: "destructive"
        });
        return;
      }

      // Create the user
      await createAdminUser(formData.email, formData.role);

      toast({
        title: "User Created Successfully!",
        description: `${formData.fullName} has been created with ${formData.role} privileges.`,
      });

      // Add to created users list
      setCreatedUsers(prev => [{
        id: Date.now().toString(), // temporary ID since we don't get one back
        email: formData.email,
        fullName: formData.fullName,
        role: formData.role,
        createdAt: new Date().toISOString()
      }, ...prev]);

      // Reset form
      setFormData({
        email: '',
        password: '',
        fullName: '',
        role: 'user'
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'super_admin':
        return <Crown className="w-4 h-4" />;
      case 'admin':
        return <Shield className="w-4 h-4" />;
      default:
        return <Users className="w-4 h-4" />;
    }
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'destructive';
      case 'admin':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  return (
    <>
      <SEOHead
        title="Create Users - Admin Panel | 1300BLK AI Sports Engine"
        description="Admin panel for creating and managing users on 1300BLK AI Sports Engine platform."
        keywords="admin panel, user management, create users, 1300BLK AI admin"
      />

      <div className="min-h-screen bg-gradient-hero">
        <Header />
        
        <main className="container mx-auto px-4 py-16">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <Badge variant="outline" className="mb-6 bg-card/80 text-primary border-primary/20">
              <Shield className="w-3 h-3 mr-1" />
              Admin Panel
            </Badge>
            
            <h1 className="text-4xl md:text-6xl font-orbitron font-black mb-6">
              <span className="bg-gradient-primary bg-clip-text text-transparent">
                Create New
              </span>
              <br />
              <span className="text-foreground">Users</span>
            </h1>
            
            <p className="text-xl text-muted-foreground font-exo max-w-3xl mx-auto">
              Create and manage user accounts with different permission levels for the 1300BLK AI platform.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
            {/* Create User Form */}
            <Card className="bg-card/80 backdrop-blur-sm border-border/50 p-8">
              <div className="flex items-center gap-3 mb-6">
                <UserPlus className="w-6 h-6 text-primary" />
                <h2 className="text-2xl font-orbitron font-bold text-foreground">
                  Create User Account
                </h2>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <Label htmlFor="fullName" className="font-rajdhani font-semibold">
                    Full Name *
                  </Label>
                  <Input
                    id="fullName"
                    type="text"
                    value={formData.fullName}
                    onChange={(e) => handleInputChange('fullName', e.target.value)}
                    className="mt-2 bg-background/50 border-border/50 font-exo"
                    required
                    placeholder="Enter full name"
                  />
                </div>

                <div>
                  <Label htmlFor="email" className="font-rajdhani font-semibold">
                    Email Address *
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="mt-2 bg-background/50 border-border/50 font-exo"
                    required
                    placeholder="Enter email address"
                  />
                </div>

                <div>
                  <Label htmlFor="password" className="font-rajdhani font-semibold">
                    Password *
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    value={formData.password}
                    onChange={(e) => handleInputChange('password', e.target.value)}
                    className="mt-2 bg-background/50 border-border/50 font-exo"
                    required
                    placeholder="Enter password (min. 6 characters)"
                    minLength={6}
                  />
                </div>

                <div>
                  <Label htmlFor="role" className="font-rajdhani font-semibold">
                    User Role *
                  </Label>
                  <Select onValueChange={(value) => handleInputChange('role', value)} defaultValue="user">
                    <SelectTrigger className="mt-2 bg-background/50 border-border/50 font-exo">
                      <SelectValue placeholder="Select user role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="user">
                        <div className="flex items-center gap-2">
                          <Users className="w-4 h-4" />
                          Regular User
                        </div>
                      </SelectItem>
                      {(isAdmin || isSuperAdmin) && (
                        <SelectItem value="admin">
                          <div className="flex items-center gap-2">
                            <Shield className="w-4 h-4" />
                            Admin
                          </div>
                        </SelectItem>
                      )}
                      {isSuperAdmin && (
                        <SelectItem value="super_admin">
                          <div className="flex items-center gap-2">
                            <Crown className="w-4 h-4" />
                            Super Admin
                          </div>
                        </SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  type="submit"
                  size="lg"
                  disabled={isSubmitting}
                  className="w-full bg-gradient-primary hover:opacity-90 text-white font-rajdhani font-semibold"
                >
                  {isSubmitting ? (
                    <>Creating User...</>
                  ) : (
                    <>
                      <UserPlus className="mr-2 w-4 h-4" />
                      Create User Account
                    </>
                  )}
                </Button>
              </form>

              {/* Permission Info */}
              <Card className="bg-gradient-primary/10 border-primary/20 backdrop-blur-sm p-4 mt-6">
                <h3 className="font-rajdhani font-semibold text-foreground mb-2">
                  Permission Levels:
                </h3>
                <ul className="text-sm text-muted-foreground font-exo space-y-1">
                  <li>• <strong>User:</strong> Basic platform access</li>
                  <li>• <strong>Admin:</strong> Manage predictions and users</li>
                  <li>• <strong>Super Admin:</strong> Full platform control</li>
                </ul>
              </Card>
            </Card>

            {/* Recently Created Users */}
            <Card className="bg-card/80 backdrop-blur-sm border-border/50 p-8">
              <div className="flex items-center gap-3 mb-6">
                <CheckCircle className="w-6 h-6 text-success" />
                <h2 className="text-2xl font-orbitron font-bold text-foreground">
                  Recently Created
                </h2>
              </div>

              {createdUsers.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4 opacity-50" />
                  <p className="text-muted-foreground font-exo">
                    No users created yet. Create your first user using the form.
                  </p>
                </div>
              ) : (
                <div className="space-y-4">
                  {createdUsers.map((user) => (
                    <div
                      key={user.id}
                      className="border border-border/30 rounded-lg p-4 bg-background/20"
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-rajdhani font-semibold text-foreground">
                          {user.fullName}
                        </h3>
                        <Badge variant={getRoleBadgeVariant(user.role)} className="flex items-center gap-1">
                          {getRoleIcon(user.role)}
                          {user.role.replace('_', ' ')}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground font-exo mb-1">
                        {user.email}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Created: {new Date(user.createdAt).toLocaleString()}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </Card>
          </div>
        </main>

        <MinimalFooter />
      </div>
    </>
  );
};

export default CreateUsers;