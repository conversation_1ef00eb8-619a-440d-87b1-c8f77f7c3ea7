import React from 'react';

const LoadingScreen: React.FC = () => {
  return (
    <div className="fixed inset-0 bg-background z-50 flex items-center justify-center">
      <div className="text-center px-4">
        {/* Logo - Responsive sizing */}
        <div className="w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 mx-auto mb-4 sm:mb-6 animate-pulse">
          <img 
            src="/lovable-uploads/d2e31152-4343-4f36-a76d-fd6714c7dc7b.png" 
            alt="1300BLK AI" 
            className="w-full h-full object-contain drop-shadow-glow"
          />
        </div>
        
        {/* Brand Text - Responsive sizing */}
        <h1 className="text-xl sm:text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bruno tracking-wide text-foreground mb-4 sm:mb-6 text-shadow-glow animate-fade-in">
          1300BLK AI
        </h1>
        
        {/* Loading Animation - Responsive sizing */}
        <div className="w-32 sm:w-40 md:w-48 lg:w-56 h-1 sm:h-1.5 bg-muted rounded-full overflow-hidden mx-auto">
          <div 
            className="h-full bg-gradient-primary rounded-full animate-[loading_2s_ease-in-out_infinite] shadow-glow"
            style={{
              animation: 'loading 2s ease-in-out infinite'
            }}
          />
        </div>
        
        {/* Status Text - Responsive sizing */}
        <p className="text-xs sm:text-sm md:text-base text-muted-foreground mt-3 sm:mt-4 md:mt-6 font-medium">
          <span className="hidden sm:inline">Initializing Sports Intelligence Engine</span>
          <span className="sm:hidden">Loading AI Engine</span>
          <span className="animate-pulse">...</span>
        </p>
        
        {/* Breakpoint indicator - Hidden in production */}
        <div className="mt-4 text-xs text-muted-foreground/50 font-mono">
          <span className="sm:hidden">Mobile</span>
          <span className="hidden sm:block md:hidden">SM</span>
          <span className="hidden md:block lg:hidden">MD</span>
          <span className="hidden lg:block xl:hidden">LG</span>
          <span className="hidden xl:block 2xl:hidden">XL</span>
          <span className="hidden 2xl:block">2XL</span>
        </div>
      </div>
      
      <style>{`
        @keyframes loading {
          0% { width: 0%; transform: translateX(-100%); }
          50% { width: 100%; transform: translateX(0%); }
          100% { width: 0%; transform: translateX(100%); }
        }
      `}</style>
    </div>
  );
};

export default LoadingScreen;