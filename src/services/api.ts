import axios, { AxiosResponse } from 'axios';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3004/api';

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface User {
  id: string;
  email: string;
  fullName?: string;
  role: 'USER' | 'ADMIN' | 'SUPER_ADMIN';
  subscriptionTier: 'FREE' | 'BASIC' | 'PREMIUM' | 'ENTERPRISE';
  subscriptionStatus?: string;
  subscriptionEnd?: string;
  emailVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  user: User;
  token: string;
}

export interface PredictionData {
  id: string;
  sport: string;
  league: string;
  homeTeam: string;
  awayTeam: string;
  matchDate: string;
  market: string;
  pick: string;
  confidence: number;
  odds: number;
  edge: number;
  expectedValue: number;
  source: string;
  status: string;
  isPremium: boolean;
  factors?: any;
  analysis?: string;
  createdAt: string;
}

export interface SportsData {
  id: string;
  matchId?: string;
  sport: string;
  league: string;
  homeTeam: string;
  awayTeam: string;
  matchDate: string;
  odds?: any;
  stats?: any;
  source: string;
  status: string;
  homeScore?: number;
  awayScore?: number;
  createdAt: string;
}

// Keep the legacy Prediction interface for backward compatibility
export interface Prediction extends PredictionData {}

class ApiService {
  // Authentication methods
  async login(email: string, password: string): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await apiClient.post('/auth/login', { email, password });

      if (response.data.success) {
        // Store token and user data
        localStorage.setItem('auth_token', response.data.data.token);
        localStorage.setItem('user', JSON.stringify(response.data.data.user));
      }

      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Login failed',
      };
    }
  }

  async register(data: { email: string; password: string; fullName?: string; role?: string }): Promise<ApiResponse<AuthResponse>> {
    try {
      const response = await apiClient.post('/auth/register', data);

      if (response.data.success) {
        // Store token and user data
        localStorage.setItem('auth_token', response.data.data.token);
        localStorage.setItem('user', JSON.stringify(response.data.data.user));
      }

      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Registration failed',
      };
    }
  }

  async logout(): Promise<void> {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user');
  }

  async getProfile(): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.get('/auth/profile');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to fetch profile',
      };
    }
  }

  getCurrentUser(): User | null {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  }

  isAuthenticated(): boolean {
    return !!localStorage.getItem('auth_token');
  }

  // Prediction methods
  async getTodaysPredictions(params: {
    sport?: string;
    league?: string;
    limit?: number;
    page?: number;
  } = {}): Promise<ApiResponse<{ predictions: PredictionData[]; pagination: any }>> {
    try {
      const queryParams = new URLSearchParams();

      if (params.sport && params.sport !== 'all') {
        queryParams.append('sport', params.sport);
      }
      if (params.league && params.league !== 'all') {
        queryParams.append('league', params.league);
      }
      if (params.limit) {
        queryParams.append('limit', params.limit.toString());
      }
      if (params.page) {
        queryParams.append('page', params.page.toString());
      }

      // Filter for today's predictions
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      queryParams.append('startDate', today.toISOString().split('T')[0]);
      queryParams.append('endDate', tomorrow.toISOString().split('T')[0]);

      const response = await apiClient.get(`/predictions?${queryParams}`);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to fetch predictions',
      };
    }
  }

  async getAllPredictions(params: {
    sport?: string;
    league?: string;
    status?: string;
    isPremium?: boolean;
    minConfidence?: number;
    page?: number;
    limit?: number;
  } = {}): Promise<ApiResponse<{ predictions: PredictionData[]; pagination: any }>> {
    try {
      const queryParams = new URLSearchParams();

      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });

      const response = await apiClient.get(`/predictions?${queryParams}`);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to fetch predictions',
      };
    }
  }

  async getPredictionById(id: string): Promise<ApiResponse<PredictionData>> {
    try {
      const response = await apiClient.get(`/predictions/${id}`);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to fetch prediction',
      };
    }
  }

  async createPrediction(predictionData: Partial<PredictionData>): Promise<ApiResponse<PredictionData>> {
    try {
      const response = await apiClient.post('/predictions/manual', predictionData);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to create prediction',
      };
    }
  }

  async generatePredictions(matchId: string, markets: string[] = ['moneyline', 'spread', 'total']): Promise<ApiResponse<PredictionData[]>> {
    try {
      const response = await apiClient.post('/predictions/generate', { matchId, markets });
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to generate predictions',
      };
    }
  }

  // Sports data methods
  async getAvailableSports(): Promise<ApiResponse<{ sport: string; leagues: string[] }[]>> {
    try {
      const response = await apiClient.get('/scraping/providers');

      if (response.data.success && response.data.data.sports) {
        const sports = Object.entries(response.data.data.sports).map(([sport, leagues]) => ({
          sport,
          leagues: leagues as string[],
        }));

        return {
          success: true,
          data: sports,
        };
      }

      // Fallback to hardcoded sports
      return {
        success: true,
        data: [
          { sport: 'football', leagues: ['nfl', 'ncaaf'] },
          { sport: 'basketball', leagues: ['nba', 'ncaab'] },
          { sport: 'baseball', leagues: ['mlb'] },
          { sport: 'hockey', leagues: ['nhl'] },
          { sport: 'soccer', leagues: ['mls', 'epl'] },
        ],
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to fetch sports',
      };
    }
  }

  async getSportsData(params: {
    sport?: string;
    league?: string;
    status?: string;
    page?: number;
    limit?: number;
  } = {}): Promise<ApiResponse<{ matches: SportsData[]; pagination: any }>> {
    try {
      const queryParams = new URLSearchParams();

      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });

      const response = await apiClient.get(`/sports-data?${queryParams}`);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to fetch sports data',
      };
    }
  }

  async getSportsDataById(id: string): Promise<ApiResponse<SportsData>> {
    try {
      const response = await apiClient.get(`/sports-data/${id}`);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to fetch sports data',
      };
    }
  }

  // Statistics and scraping methods
  async getAccuracyStats(params: {
    sport?: string;
    timeframe?: string;
  } = {}): Promise<ApiResponse<any>> {
    try {
      const response = await apiClient.get('/predictions/stats');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to fetch accuracy stats',
      };
    }
  }

  async getPredictionStats(): Promise<ApiResponse<any>> {
    try {
      const response = await apiClient.get('/predictions/stats');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to fetch prediction stats',
      };
    }
  }

  async getSportsDataStats(): Promise<ApiResponse<any>> {
    try {
      const response = await apiClient.get('/sports-data/stats');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to fetch sports data stats',
      };
    }
  }

  async startScraping(provider: string, sport: string, league?: string): Promise<ApiResponse<any>> {
    try {
      const response = await apiClient.post('/scraping/scrape', { provider, sport, league });
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to start scraping',
      };
    }
  }

  async startComprehensiveScraping(sport: string, league?: string): Promise<ApiResponse<any>> {
    try {
      const response = await apiClient.post('/scraping/scrape-all', { sport, league });
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to start comprehensive scraping',
      };
    }
  }

  async getScrapingJobs(): Promise<ApiResponse<any>> {
    try {
      const response = await apiClient.get('/scraping/jobs');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to fetch scraping jobs',
      };
    }
  }

  // Admin Subscription methods
  async getAdminSubscriptions(): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.get('/admin/subscriptions');
      return response.data;
    } catch (error: any) {
      return { success: false, error: error.response?.data?.error || 'Failed to fetch subscriptions' };
    }
  }

  async cancelSubscription(subscriptionId: string): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.post(`/admin/subscriptions/${subscriptionId}/cancel`);
      return response.data;
    } catch (error: any) {
      return { success: false, error: error.response?.data?.error || 'Failed to cancel subscription' };
    }
  }

  // Admin Ad methods
  async getAdUnits(): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.get('/admin/ads');
      return response.data;
    } catch (error: any) {
      return { success: false, error: error.response?.data?.error || 'Failed to fetch ad units' };
    }
  }

  async updateAdStatus(adId: string, status: string): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.patch(`/admin/ads/${adId}/status`, { status });
      return response.data;
    } catch (error: any) {
      return { success: false, error: error.response?.data?.error || 'Failed to update ad status' };
    }
  }

  async deleteAdUnit(adId: string): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.delete(`/admin/ads/${adId}`);
      return response.data;
    } catch (error: any) {
      return { success: false, error: error.response?.data?.error || 'Failed to delete ad unit' };
    }
  }

  // Admin ML Model methods
  async getMLModels(): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.get('/admin/models');
      return response.data;
    } catch (error: any) {
      return { success: false, error: error.response?.data?.error || 'Failed to fetch ML models' };
    }
  }

  async startModelTraining(modelId: string): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.post(`/admin/models/${modelId}/train`);
      return response.data;
    } catch (error: any) {
      return { success: false, error: error.response?.data?.error || 'Failed to start model training' };
    }
  }

  async updateModelStatus(modelId: string, status: string): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.patch(`/admin/models/${modelId}/status`, { status });
      return response.data;
    } catch (error: any) {
      return { success: false, error: error.response?.data?.error || 'Failed to update model status' };
    }
  }

  // User Favorites methods
  async getUserFavoritePredictions(): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.get('/user/favorites/predictions');
      return response.data;
    } catch (error: any) {
      return { success: false, error: error.response?.data?.error || 'Failed to fetch favorite predictions' };
    }
  }

  async getUserFavoriteTeams(): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.get('/user/favorites/teams');
      return response.data;
    } catch (error: any) {
      return { success: false, error: error.response?.data?.error || 'Failed to fetch favorite teams' };
    }
  }

  async getUserFavoriteLeagues(): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.get('/user/favorites/leagues');
      return response.data;
    } catch (error: any) {
      return { success: false, error: error.response?.data?.error || 'Failed to fetch favorite leagues' };
    }
  }

  async removeFavoritePrediction(predictionId: string): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.delete(`/user/favorites/predictions/${predictionId}`);
      return response.data;
    } catch (error: any) {
      return { success: false, error: error.response?.data?.error || 'Failed to remove favorite prediction' };
    }
  }

  async removeFavoriteTeam(teamId: string): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.delete(`/user/favorites/teams/${teamId}`);
      return response.data;
    } catch (error: any) {
      return { success: false, error: error.response?.data?.error || 'Failed to remove favorite team' };
    }
  }

  async removeFavoriteLeague(leagueId: string): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.delete(`/user/favorites/leagues/${leagueId}`);
      return response.data;
    } catch (error: any) {
      return { success: false, error: error.response?.data?.error || 'Failed to remove favorite league' };
    }
  }

  async getUserStats(): Promise<ApiResponse<any>> {
    try {
      const response = await apiClient.get('/users/stats');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to fetch user stats',
      };
    }
  }

  async getUsers(params: {
    page?: number;
    limit?: number;
    role?: string;
  } = {}): Promise<ApiResponse<{ users: User[]; pagination: any }>> {
    try {
      const queryParams = new URLSearchParams();

      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });

      const response = await apiClient.get(`/users?${queryParams}`);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to fetch users',
      };
    }
  }

  async getUserById(id: string): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.get(`/users/${id}`);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to fetch user',
      };
    }
  }

  async updateUser(id: string, updates: Partial<User>): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.put(`/users/${id}`, updates);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to update user',
      };
    }
  }

  async deleteUser(id: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiClient.delete(`/users/${id}`);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to delete user',
      };
    }
  }



  async updatePrediction(id: string, updates: Partial<PredictionData>): Promise<ApiResponse<PredictionData>> {
    try {
      const response = await apiClient.put(`/predictions/${id}`, updates);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to update prediction',
      };
    }
  }

  async deletePrediction(id: string): Promise<ApiResponse<void>> {
    try {
      const response = await apiClient.delete(`/predictions/${id}`);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to delete prediction',
      };
    }
  }

  async updateProfile(updates: { fullName?: string; email?: string }): Promise<ApiResponse<User>> {
    try {
      const response = await apiClient.put('/auth/profile', updates);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to update profile',
      };
    }
  }

  async changePassword(data: { currentPassword: string; newPassword: string }): Promise<ApiResponse<void>> {
    try {
      const response = await apiClient.put('/auth/change-password', data);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to change password',
      };
    }
  }

  // Global Settings methods
  async getGlobalSettings(): Promise<ApiResponse<Array<{ key: string; value: string }>>> {
    try {
      const response: AxiosResponse<ApiResponse<Array<{ key: string; value: string }>>> = await apiClient.get('/admin/settings');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to fetch global settings',
      };
    }
  }

  async updateGlobalSetting(key: string, value: any): Promise<ApiResponse<{ key: string; value: string }>> {
    try {
      const response: AxiosResponse<ApiResponse<{ key: string; value: string }>> = await apiClient.put(`/admin/settings/${key}`, { value });
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to update global setting',
      };
    }
  }

  // Payment methods
  async createPaystackPayment(data: any): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.post('/payments/paystack', data);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to create Paystack payment',
      };
    }
  }

  async createFlutterwavePayment(data: any): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.post('/payments/flutterwave', data);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to create Flutterwave payment',
      };
    }
  }

  async createCryptoPayment(data: any): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.post('/payments/crypto', data);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to create crypto payment',
      };
    }
  }

  // Data scraping methods
  async scrapeWebData(data: any): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.post('/scraping/web-scraper', data);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to scrape web data',
      };
    }
  }

  // Automation methods
  async getAutomationStatus(): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.get('/automation/status');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to get automation status',
      };
    }
  }

  async startAutomation(config: any): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.post('/automation/start', config);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to start automation',
      };
    }
  }

  async stopAutomation(): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.post('/automation/stop');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to stop automation',
      };
    }
  }

  async executePipeline(data: any): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.post('/automation/execute-pipeline', data);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to execute pipeline',
      };
    }
  }

  async updateAutomationConfig(config: any): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.put('/automation/config', config);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to update automation config',
      };
    }
  }

  async getAutomationMetrics(timeframe: string = '24h'): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.get(`/automation/metrics?timeframe=${timeframe}`);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to get automation metrics',
      };
    }
  }

  async testAutomation(component: string = 'all'): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.post('/automation/test', { component });
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to test automation',
      };
    }
  }

  async processSportsData(data: any): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.post('/scraping/process-sports-data', data);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to process sports data',
      };
    }
  }

  async generatePrediction(data: any): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.post('/predictions/generate', data);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to generate prediction',
      };
    }
  }

  async saveRawData(data: any): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.post('/scraping/raw-data', data);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to save raw data',
      };
    }
  }

  // User profile methods
  async getUserProfile(): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.get('/auth/profile');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to get user profile',
      };
    }
  }

  // Payment management methods
  async getPaymentTransactions(): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.get('/admin/payments');
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to get payment transactions',
      };
    }
  }

  async createManualPayment(data: any): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.post('/admin/payments/manual', data);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to create manual payment',
      };
    }
  }

  async refundPayment(data: any): Promise<ApiResponse<any>> {
    try {
      const response: AxiosResponse<ApiResponse<any>> = await apiClient.post('/admin/payments/refund', data);
      return response.data;
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || 'Failed to refund payment',
      };
    }
  }


}

export const apiService = new ApiService();
export const api = apiService; // Alias for backward compatibility
export default apiService; // Default export
