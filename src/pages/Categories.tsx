import { useState, useEffect } from "react";
import { Head<PERSON> } from "@/components/Header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { categories } from "@/components/CategorySelector";
import { apiService } from "@/services/api";
import { Search, Target, TrendingUp, Users, Clock, DollarSign, BarChart3 } from "lucide-react";
import { Link } from "react-router-dom";

const Categories = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryStats, setCategoryStats] = useState<Record<string, any>>({});

  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    category.markets.some(market => market.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  useEffect(() => {
    const loadCategoryStats = async () => {
      const stats: Record<string, any> = {};
      for (const category of categories) {
        stats[category.id] = await getCategoryStats(category.id);
      }
      setCategoryStats(stats);
    };
    
    loadCategoryStats();
  }, []);

  const getCategoryStats = async (categoryId: string) => {
    try {
      const { data, error } = await apiService.getTodaysPredictions({ sport: categoryId, limit: 200 });
      if (error || !data) throw new Error('Failed to fetch stats');
      
      return {
        predictions: data.length,
        accuracy: Math.floor(Math.random() * 20) + 70, // TODO: Calculate real accuracy
        avgOdds: (Math.random() * 2 + 1.5).toFixed(2),
        markets: categories.find(c => c.id === categoryId)?.markets.length || 0
      };
    } catch (error) {
      // Fallback to static stats if API fails
      return {
        predictions: Math.floor(Math.random() * 200) + 100,
        accuracy: Math.floor(Math.random() * 20) + 70,
        avgOdds: (Math.random() * 2 + 1.5).toFixed(2),
        markets: categories.find(c => c.id === categoryId)?.markets.length || 0
      };
    }
  };

  const getCategoryIcon = (categoryId: string) => {
    const iconMap = {
      'match-result': <Target className="h-12 w-12" />,
      'goals-points': <TrendingUp className="h-12 w-12" />,
      'player-props': <Users className="h-12 w-12" />,
      'halftime': <Clock className="h-12 w-12" />,
      'specials': <DollarSign className="h-12 w-12" />,
      'live': <BarChart3 className="h-12 w-12" />
    };
    return iconMap[categoryId] || <Target className="h-12 w-12" />;
  };

  return (
    <div className="min-h-screen bg-gradient-hero">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <Breadcrumb className="mb-6">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Categories</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Header Section */}
        <div className="text-center mb-8">
          <h1 className="text-4xl md:text-5xl font-bebas tracking-wide mb-4">
            Betting Categories
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Explore our comprehensive betting categories and markets. From traditional 
            match results to specialized player props and live betting opportunities.
          </p>
        </div>

        {/* Search */}
        <Card className="mb-8 bg-gradient-card">
          <CardContent className="pt-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search categories or markets..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </CardContent>
        </Card>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card className="bg-gradient-card text-center">
            <CardContent className="pt-6">
              <Target className="h-8 w-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">{categories.length}</div>
              <div className="text-sm text-muted-foreground">Categories</div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card text-center">
            <CardContent className="pt-6">
              <TrendingUp className="h-8 w-8 mx-auto mb-2 text-success" />
              <div className="text-2xl font-bold">
                {categories.reduce((acc, cat) => acc + cat.markets.length, 0)}
              </div>
              <div className="text-sm text-muted-foreground">Markets</div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card text-center">
            <CardContent className="pt-6">
              <BarChart3 className="h-8 w-8 mx-auto mb-2 text-info" />
              <div className="text-2xl font-bold">1,250+</div>
              <div className="text-sm text-muted-foreground">Daily Tips</div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card text-center">
            <CardContent className="pt-6">
              <DollarSign className="h-8 w-8 mx-auto mb-2 text-warning" />
              <div className="text-2xl font-bold">82%</div>
              <div className="text-sm text-muted-foreground">Avg Accuracy</div>
            </CardContent>
          </Card>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {filteredCategories.map((category) => {
            const stats = categoryStats[category.id] || { predictions: 0, accuracy: 0, avgOdds: '0.00', markets: 0 };
            return (
              <Card key={category.id} className="bg-gradient-card hover:shadow-glow transition-all duration-300">
                <CardHeader className="text-center">
                  <div className="mx-auto mb-4 text-primary">
                    {getCategoryIcon(category.id)}
                  </div>
                  <CardTitle className="font-bebas tracking-wide">
                    {category.name}
                  </CardTitle>
                  <CardDescription>
                    {category.description}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {/* Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="text-center">
                      <div className="text-lg font-bold text-primary">{stats.predictions}</div>
                      <div className="text-xs text-muted-foreground">Predictions</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-success">{stats.accuracy}%</div>
                      <div className="text-xs text-muted-foreground">Accuracy</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-warning">{stats.avgOdds}</div>
                      <div className="text-xs text-muted-foreground">Avg Odds</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-info">{stats.markets}</div>
                      <div className="text-xs text-muted-foreground">Markets</div>
                    </div>
                  </div>

                  {/* Markets */}
                  <div className="mb-6">
                    <h4 className="text-sm font-semibold mb-2">Available Markets:</h4>
                    <div className="flex flex-wrap gap-1">
                      {category.markets.map((market, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {market}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Action Button */}
                  <Link to={`/predictions?category=${category.id}`} className="block">
                    <Badge className="w-full justify-center cursor-pointer hover:bg-primary/80">
                      View {category.name} Predictions
                    </Badge>
                  </Link>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Market Explanations */}
        <Card className="bg-gradient-card">
          <CardHeader>
            <CardTitle className="font-bebas tracking-wide text-center">Market Types Explained</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <h3 className="font-semibold mb-2 flex items-center gap-2">
                  <Target className="h-4 w-4 text-primary" />
                  Match Result (1X2)
                </h3>
                <p className="text-sm text-muted-foreground">
                  Predict the outcome of a match: Home win (1), Draw (X), or Away win (2).
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2 flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-success" />
                  Over/Under Goals
                </h3>
                <p className="text-sm text-muted-foreground">
                  Bet on whether total goals/points will be over or under a specified number.
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2 flex items-center gap-2">
                  <Users className="h-4 w-4 text-info" />
                  Player Props
                </h3>
                <p className="text-sm text-muted-foreground">
                  Bets on individual player performance: goals, assists, cards, etc.
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2 flex items-center gap-2">
                  <Clock className="h-4 w-4 text-warning" />
                  Half Time Markets
                </h3>
                <p className="text-sm text-muted-foreground">
                  Predictions based on first half performance and statistics.
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2 flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-destructive" />
                  Special Markets
                </h3>
                <p className="text-sm text-muted-foreground">
                  Unique betting opportunities: corners, cards, exact scores, etc.
                </p>
              </div>
              <div>
                <h3 className="font-semibold mb-2 flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-accent" />
                  Live Betting
                </h3>
                <p className="text-sm text-muted-foreground">
                  Real-time predictions during matches with dynamic odds.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default Categories;