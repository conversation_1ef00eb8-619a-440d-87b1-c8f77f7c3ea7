import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { NeumorphicCard } from '@/components/ui/neumorphic-card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { AdSenseHeader, AdSenseSidebar, AdSenseInline } from '@/components/AdSense';
import { TrendingUp, Star, Target, Calendar, Crown, Settings, RefreshCw, Activity, DollarSign, Trophy } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import { useAuth } from '@/contexts/AuthContext';
import { apiService as api, PredictionData } from '@/services/api';
import { useToast } from '@/hooks/use-toast';
import AthleteAdCard from '@/components/AthleteAdCard';

interface UserStats {
  totalPredictions: number;
  winningPredictions: number;
  winRate: number;
  recentPredictions: PredictionData[];
  currentStreak: number;
  totalProfit: number;
  avgConfidence: number;
}

const UserDashboard: React.FC = () => {
  const isMobile = useIsMobile();
  const { user } = useAuth();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<UserStats | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const fetchUserStats = async () => {
    try {
      setLoading(true);

      // Fetch user's predictions
      const predictionsResponse = await api.getAllPredictions({
        limit: 10,
        page: 1
      });

      if (predictionsResponse.success && predictionsResponse.data) {
        const predictions = predictionsResponse.data.predictions;

        // Calculate user stats
        const totalPredictions = predictions.length;
        const winningPredictions = predictions.filter(p => p.status === 'WIN').length;
        const winRate = totalPredictions > 0 ? (winningPredictions / totalPredictions) * 100 : 0;

        // Calculate profit (simplified)
        const totalProfit = predictions.reduce((sum, p) => {
          if (p.status === 'WIN') {
            return sum + (p.odds > 0 ? p.odds / 100 : 100 / Math.abs(p.odds));
          } else if (p.status === 'LOSS') {
            return sum - 1;
          }
          return sum;
        }, 0);

        // Calculate average confidence
        const avgConfidence = totalPredictions > 0
          ? predictions.reduce((sum, p) => sum + p.confidence, 0) / totalPredictions
          : 0;

        // Calculate current streak (simplified)
        let currentStreak = 0;
        for (let i = 0; i < predictions.length; i++) {
          if (predictions[i].status === 'WIN') {
            currentStreak++;
          } else if (predictions[i].status === 'LOSS') {
            break;
          }
        }

        setStats({
          totalPredictions,
          winningPredictions,
          winRate,
          recentPredictions: predictions.slice(0, 5),
          currentStreak,
          totalProfit,
          avgConfidence
        });
      }
    } catch (error) {
      console.error('Error fetching user stats:', error);
      toast({
        title: "Error",
        description: "Failed to load dashboard data",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchUserStats();
  };

  useEffect(() => {
    fetchUserStats();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-hero">
      <AdSenseHeader />

      <div className="container mx-auto px-4 py-8">
        <div className={`grid grid-cols-1 ${isMobile ? '' : 'lg:grid-cols-4'} gap-6`}>
          {/* Main Content */}
          <div className={`${isMobile ? '' : 'lg:col-span-3'} space-y-6`}>
            {/* Welcome Section */}
            <div className={`${isMobile ? 'text-center space-y-4' : 'flex items-center justify-between'}`}>
              <div>
                <h1 className="text-3xl font-bruno tracking-wide">
                  Welcome Back{user?.fullName ? `, ${user.fullName}` : ''}!
                </h1>
                <p className="text-muted-foreground">Track your predictions and manage your account</p>
              </div>
              <div className="flex items-center gap-4">
                <Button
                  onClick={handleRefresh}
                  disabled={refreshing}
                  variant="outline"
                  size="sm"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
                {user && (
                  <Badge variant="secondary" className="bg-gradient-primary text-primary-foreground">
                    <Crown className="h-4 w-4 mr-1" />
                    {user.subscriptionTier}
                  </Badge>
                )}
              </div>
            </div>

            {/* Stats Cards */}
            <div className={`grid grid-cols-1 ${isMobile ? 'grid-cols-2' : 'md:grid-cols-4'} gap-4`}>
              {loading ? (
                Array.from({ length: 4 }).map((_, i) => (
                  <Card key={i} className="bg-gradient-card">
                    <CardContent className="p-4 text-center">
                      <Skeleton className="h-8 w-8 mx-auto mb-2 rounded-lg" />
                      <Skeleton className="h-6 w-12 mx-auto mb-1" />
                      <Skeleton className="h-4 w-16 mx-auto" />
                    </CardContent>
                  </Card>
                ))
              ) : stats ? (
                 <>
                   <NeumorphicCard hover className="text-center">
                     <Target className="h-8 w-8 mx-auto text-primary mb-2" />
                     <div className="text-2xl font-bold">{stats.totalPredictions}</div>
                     <div className="text-sm text-muted-foreground">Total Predictions</div>
                   </NeumorphicCard>

                   <NeumorphicCard hover className="text-center">
                     <TrendingUp className="h-8 w-8 mx-auto text-success mb-2" />
                     <div className="text-2xl font-bold">{stats.winRate.toFixed(1)}%</div>
                     <div className="text-sm text-muted-foreground">Win Rate</div>
                   </NeumorphicCard>

                   <NeumorphicCard hover className="text-center">
                     <Star className="h-8 w-8 mx-auto text-warning mb-2" />
                     <div className="text-2xl font-bold">{stats.currentStreak}</div>
                     <div className="text-sm text-muted-foreground">Win Streak</div>
                   </NeumorphicCard>

                   <NeumorphicCard hover className="text-center">
                     <DollarSign className="h-8 w-8 mx-auto text-info mb-2" />
                     <div className="text-2xl font-bold">{stats.totalProfit > 0 ? '+' : ''}{stats.totalProfit.toFixed(2)}</div>
                     <div className="text-sm text-muted-foreground">Total Profit</div>
                   </NeumorphicCard>
                 </>
              ) : (
                <div className="col-span-4 text-center py-8">
                  <p className="text-muted-foreground">No data available</p>
                </div>
              )}
            </div>

            <AdSenseInline />

            {/* Recent Predictions */}
            <Card className="neumorphic">
              <CardHeader>
                <CardTitle>Recent Predictions</CardTitle>
                <CardDescription>Your latest betting activity</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {loading ? (
                    Array.from({ length: 3 }).map((_, i) => (
                      <div key={i} className="neumorphic-inset p-3 rounded-md h-16">
                        <div className="animate-pulse space-y-2">
                          <div className="h-4 bg-muted/20 rounded w-3/4"></div>
                          <div className="h-3 bg-muted/20 rounded w-1/2"></div>
                        </div>
                      </div>
                    ))
                  ) : stats && stats.recentPredictions.length > 0 ? (
                    stats.recentPredictions.map((prediction) => (
                      <div key={prediction.id} className={`neumorphic micro-bounce flex ${isMobile ? 'flex-col space-y-2' : 'items-center justify-between'} p-3 rounded-md`}>
                        <div className="flex items-center space-x-4">
                          <Badge variant="secondary" className="neumorphic border-none">
                            {prediction.sport.charAt(0).toUpperCase() + prediction.sport.slice(1)}
                          </Badge>
                          <div>
                            <div className="font-medium">
                              {prediction.homeTeam} vs {prediction.awayTeam}
                            </div>
                            <div className="text-sm text-muted-foreground">
                              {prediction.market} • {prediction.pick}
                            </div>
                          </div>
                        </div>
                        <div className={`${isMobile ? 'text-left' : 'text-right'}`}>
                          <div className="flex items-center gap-2">
                            <Badge
                              variant={
                                prediction.status === 'WIN' ? 'default' :
                                prediction.status === 'LOSS' ? 'destructive' :
                                'secondary'
                              }
                              className="text-xs"
                            >
                              {prediction.status || 'Pending'}
                            </Badge>
                            <span className="text-sm font-medium">
                              {prediction.confidence}%
                            </span>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {prediction.odds > 0 ? '+' : ''}{prediction.odds}
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      <Target className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <p>No predictions yet</p>
                      <p className="text-sm">Start by browsing our latest predictions</p>
                    </div>
                  )}
                </div>
                <Button variant="outline" className="w-full mt-4" asChild>
                  <Link to="/user/predictions">View All Predictions</Link>
                </Button>
              </CardContent>
            </Card>

            {/* Quick Actions */}
            <Card className="bg-gradient-card">
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <Button variant="outline" asChild>
                  <Link to="/predictions">Browse Predictions</Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link to="/user/favorites">My Favorites</Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link to="/user/subscription">Subscription</Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link to="/user/settings">
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          {!isMobile && (
            <div className="space-y-6">
              <AdSenseSidebar />
              
              {/* Athlete AI Ad Card */}
              <AthleteAdCard />
            
            <Card className="bg-gradient-card">
              <CardHeader>
                <CardTitle className="text-lg">Upgrade to Pro</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Get access to premium predictions and advanced analytics.
                </p>
                <Button className="w-full bg-gradient-primary" asChild>
                  <Link to="/user/subscription">Upgrade Now</Link>
                </Button>
              </CardContent>
            </Card>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserDashboard;