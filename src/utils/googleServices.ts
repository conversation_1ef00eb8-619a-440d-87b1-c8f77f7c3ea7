
// Google Search Console Setup Guide
export const GOOGLE_SEARCH_CONSOLE_SETUP = {
  steps: [
    {
      step: 1,
      title: "Verify Domain Ownership",
      description: "Add your domain to Google Search Console at https://search.google.com/search-console",
      actions: [
        "Go to Google Search Console",
        "Click 'Add Property'",
        "Choose 'Domain' verification method",
        "Add DNS TXT record to your domain provider",
        "Verify ownership"
      ]
    },
    {
      step: 2,
      title: "Submit Sitemap",
      description: "Submit your sitemap to help Google crawl your site",
      actions: [
        "In Search Console, go to 'Sitemaps'",
        "Submit: https://1300blk.online/sitemap.xml",
        "Monitor indexing status"
      ]
    },
    {
      step: 3,
      title: "Set up Core Web Vitals Monitoring",
      description: "Monitor your site's performance metrics",
      actions: [
        "Check 'Core Web Vitals' report",
        "Monitor page loading speeds",
        "Fix any performance issues identified"
      ]
    }
  ]
};

// Google AdSense Setup Guide  
export const GOOGLE_ADSENSE_SETUP = {
  steps: [
    {
      step: 1,
      title: "Create AdSense Account",
      description: "Set up your Google AdSense account",
      actions: [
        "Go to www.google.com/adsense",
        "Sign up with your Google account",
        "Add your website URL",
        "Select your country/territory",
        "Choose payment currency"
      ]
    },
    {
      step: 2,
      title: "Add AdSense Code",
      description: "Add the AdSense code to your site header",
      code: `<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-XXXXXXXXXX" crossorigin="anonymous"></script>`,
      actions: [
        "Copy your AdSense code from the dashboard",
        "Add it to your site's <head> section",
        "Wait for Google's review (can take up to 14 days)"
      ]
    },
    {
      step: 3,
      title: "Create Ad Units",
      description: "Set up different ad formats for your site",
      actions: [
        "Create Display ads for sidebars",
        "Create In-feed ads for content sections", 
        "Create Multiplex ads for related content",
        "Test ad placements for optimal performance"
      ]
    },
    {
      step: 4,
      title: "Optimize Ad Performance",
      description: "Monitor and optimize your ad revenue",
      actions: [
        "Use Auto ads for automatic placement",
        "Monitor performance in AdSense dashboard",
        "A/B test different ad positions",
        "Ensure ads don't hurt user experience"
      ]
    }
  ],
  policies: [
    "Ensure content complies with AdSense policies",
    "Don't click on your own ads",
    "Provide valuable, original content",
    "Have clear privacy policy and terms of service",
    "Maintain good user experience"
  ]
};

// AdSense Integration Helper
export const createAdSenseScript = (publisherId: string) => {
  return `<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-${publisherId}" crossorigin="anonymous"></script>`;
};

// Performance tracking for AdSense
export const trackAdPerformance = () => {
  // Check if gtag is available (Google Analytics 4)
  if (typeof window !== 'undefined' && 'gtag' in window && typeof window.gtag === 'function') {
    window.gtag('event', 'ad_impression', {
      event_category: 'AdSense',
      event_label: 'Ad Viewed'
    });
  }
};

// Declare gtag as a global function for TypeScript
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
  }
}
