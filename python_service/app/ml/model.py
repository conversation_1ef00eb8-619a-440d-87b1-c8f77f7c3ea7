from typing import Dict, Any, List, Optional
import os
import numpy as np

# Optional TF/Keras
try:
    import tensorflow as tf  # type: ignore
    from tensorflow import keras  # type: ignore
    TF_AVAILABLE = True
except Exception:
    TF_AVAILABLE = False

from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
from joblib import dump, load


MODEL_PATH = os.getenv("MODEL_DIR", "./models")
BASELINE_MODEL_FILE = os.path.join(MODEL_PATH, "baseline_clf.joblib")


def ensure_model_dir() -> None:
    os.makedirs(MODEL_PATH, exist_ok=True)


def train_baseline(X: np.ndarray, y: np.ndarray) -> Pipeline:
    ensure_model_dir()
    clf = Pipeline([
        ("scaler", StandardScaler()),
        ("logit", LogisticRegression(max_iter=1000)),
    ])
    clf.fit(X, y)
    dump(clf, BASELINE_MODEL_FILE)
    return clf


def load_baseline() -> Optional[Pipeline]:
    try:
        return load(BASELINE_MODEL_FILE)  # type: ignore
    except Exception:
        return None


def predict_proba(features: List[Dict[str, Any]]) -> List[float]:
    # Simple feature mapping: Expect keys like 'home_rating', 'away_rating', 'edge', etc.
    keys = sorted({k for f in features for k in f.keys()})
    X = np.array([[f.get(k, 0.0) for k in keys] for f in features], dtype=float)

    model = load_baseline()
    if model is None:
        # Train a dummy model if none exists (balanced dummy labels)
        y_dummy = np.random.randint(0, 2, size=(len(features) or 10,))
        X_dummy = np.random.randn(len(y_dummy), X.shape[1] if X.size else 3)
        model = train_baseline(X_dummy, y_dummy)

    probs = model.predict_proba(X) if X.size else np.array([[0.5, 0.5]])
    # Return probability of class 1 (e.g., home win)
    return [float(p[1]) for p in probs]


def expected_value(prob: float, odds_american: float) -> float:
    # EV = p*payoff - (1-p)*stake, using American odds
    if odds_american > 0:
        payoff = odds_american / 100.0
    else:
        payoff = 100.0 / abs(odds_american)
    return prob * payoff - (1 - prob) * 1.0
