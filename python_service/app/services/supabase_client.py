from supabase import create_client, Client
from app.config import settings
from typing import Any, Dict, List


class SupabaseService:
    def __init__(self) -> None:
        self.client: Client = create_client(
            settings.SUPABASE_URL,
            settings.SUPABASE_SERVICE_ROLE_KEY,
        )

    def upsert(self, table: str, rows: List[Dict[str, Any]]) -> int:
        if not rows:
            return 0
        res = self.client.table(table).upsert(rows).execute()
        return len(rows)

    def insert(self, table: str, rows: List[Dict[str, Any]]) -> int:
        if not rows:
            return 0
        res = self.client.table(table).insert(rows).execute()
        return len(rows)

    def select(self, table: str, columns: str = "*", **filters: Any):
        q = self.client.table(table).select(columns)
        for k, v in filters.items():
            q = q.eq(k, v)
        return q.execute().data


supabase_service = SupabaseService()
