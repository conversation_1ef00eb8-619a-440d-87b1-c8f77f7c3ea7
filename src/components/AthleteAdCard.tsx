import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import athleteA<PERSON> from '/lovable-uploads/bede4b70-a73c-4c18-a856-427669b7e2c4.png';
const AthleteAdCard: React.FC = () => {
  return <Card className="bg-gradient-card relative overflow-hidden">
      <CardContent className="p-0 relative">
        {/* Background Image - Increased height */}
        <div className="relative h-80">
          <img src={athleteAI} alt="AI Sports Prediction" className="w-full h-full object-cover object-center" />
          
          {/* Overlay with enhanced green glow */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
          <div className="absolute inset-0 bg-green-500/20 blur-xl" />
          
          {/* Content - Centered */}
          <div className="absolute inset-0 flex flex-col items-center justify-center text-center px-4">
            <Badge variant="secondary" className="bg-primary/90 text-primary-foreground mb-4 backdrop-blur-sm">
              PREDICTION ACCURACY
            </Badge>
            
            <div className="text-white">
              <div className="text-4xl font-bruno tracking-wide mb-2">
                99.8%
              </div>
              <p className="text-sm opacity-90">
                AI-Powered Success Rate
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>;
};
export default AthleteAdCard;