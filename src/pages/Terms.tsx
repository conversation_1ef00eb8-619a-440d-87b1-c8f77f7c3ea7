import React from 'react';
import { Head<PERSON> } from '@/components/Header';
import <PERSON><PERSON><PERSON>ooter from '@/components/MinimalFooter';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { SEOHead } from '@/components/SEOHead';

const Terms: React.FC = () => {
  return (
    <>
      <SEOHead
        title="Terms of Service - 1300BLK AI Sports Predictions"
        description="Read our terms of service for using 1300BLK AI sports prediction platform. Understanding your rights and responsibilities."
        canonical="https://1300blk.online/terms"
      />
      
      <div className="min-h-screen bg-gradient-hero">
        <Header />
        
        <div className="container mx-auto px-4 py-16">
          <div className="max-w-4xl mx-auto">
            <Card className="bg-gradient-card border-muted/30">
              <CardHeader className="text-center pb-8">
                <CardTitle className="text-4xl font-bebas tracking-wide mb-4">
                  Terms of Service
                </CardTitle>
                <p className="text-muted-foreground">Last updated: August 31, 2025</p>
              </CardHeader>
              
              <CardContent className="prose prose-neutral dark:prose-invert max-w-none space-y-8">
                <section>
                  <h2 className="text-2xl font-semibold mb-4">1. Acceptance of Terms</h2>
                  <p className="text-muted-foreground leading-relaxed">
                    By accessing and using 1300BLK AI ("the Service"), you accept and agree to be bound by the terms and provision of this agreement.
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold mb-4">2. Use License</h2>
                  <p className="text-muted-foreground leading-relaxed mb-4">
                    Permission is granted to temporarily access and use the Service for personal, non-commercial transitory viewing only. This is the grant of a license, not a transfer of title, and under this license you may not:
                  </p>
                  <ul className="list-disc list-inside text-muted-foreground space-y-2 ml-4">
                    <li>modify or copy the materials</li>
                    <li>use the materials for any commercial purpose or for any public display</li>
                    <li>attempt to reverse engineer any software contained on the Service</li>
                    <li>remove any copyright or other proprietary notations from the materials</li>
                  </ul>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold mb-4">3. Sports Predictions Disclaimer</h2>
                  <p className="text-muted-foreground leading-relaxed">
                    Our AI-powered sports predictions are for informational and entertainment purposes only. Past performance does not guarantee future results. All predictions involve risk, and you should never bet more than you can afford to lose. We are not responsible for any financial losses that may result from following our predictions.
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold mb-4">4. User Accounts</h2>
                  <p className="text-muted-foreground leading-relaxed">
                    You are responsible for maintaining the confidentiality of your account and password. You agree to accept responsibility for all activities that occur under your account or password.
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold mb-4">5. Prohibited Uses</h2>
                  <p className="text-muted-foreground leading-relaxed mb-4">
                    You may not use our Service:
                  </p>
                  <ul className="list-disc list-inside text-muted-foreground space-y-2 ml-4">
                    <li>For any unlawful purpose or to solicit others to perform such acts</li>
                    <li>To violate any international, federal, provincial, or state regulations, rules, laws, or local ordinances</li>
                    <li>To infringe upon or violate our intellectual property rights or the intellectual property rights of others</li>
                    <li>To harass, abuse, insult, harm, defame, slander, disparage, intimidate, or discriminate</li>
                    <li>To submit false or misleading information</li>
                  </ul>
                </section>
                
                <section>
                <h2 className="text-2xl font-semibold mb-4">6. Subscription and Payment Terms</h2>
                <p className="text-muted-foreground leading-relaxed">
                Premium subscriptions are billed in advance on a monthly or annual basis. You may cancel your subscription at any time. Refunds are handled on a case-by-case basis.
                </p>
                  <ul className="list-disc list-inside text-muted-foreground space-y-2 ml-4 mt-3">
                                    <li>Pricing and taxes are displayed prior to checkout</li>
                    <li>Promotions or coupons may be applied at checkout when available</li>
                  <li>Failed payments may result in temporary suspension until resolved</li>
                </ul>
                </section>
                
                <section>
                   <h2 className="text-2xl font-semibold mb-4">7. Responsible Use & Age</h2>
                   <p className="text-muted-foreground leading-relaxed">
                     You confirm that you are at least 18 years old or the legal age of majority in your jurisdiction. Our predictions are for informational and entertainment purposes. Please use the Service responsibly.
                   </p>
                 </section>
 
                 <section>
                   <h2 className="text-2xl font-semibold mb-4">8. Privacy Policy</h2>
                   <p className="text-muted-foreground leading-relaxed">
                     Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service.
                   </p>
                 </section>
 
                 <section>
                   <h2 className="text-2xl font-semibold mb-4">9. Data & Intellectual Property</h2>
                   <p className="text-muted-foreground leading-relaxed">
                     All content, trademarks, and data presented on the Service are owned by 1300BLK AI or our licensors and are protected by applicable intellectual property laws. You may not reproduce, resell, or redistribute any part of the Service without permission.
                   </p>
                 </section>

                <section>
                  <h2 className="text-2xl font-semibold mb-4">8. Limitation of Liability</h2>
                  <p className="text-muted-foreground leading-relaxed">
                    In no event shall 1300BLK AI or its suppliers be liable for any damages (including, without limitation, damages for loss of data or profit, or due to business interruption) arising out of the use or inability to use the Service.
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold mb-4">9. Modifications</h2>
                  <p className="text-muted-foreground leading-relaxed">
                    We reserve the right to modify these terms at any time. We will notify users of any material changes via email or through the Service.
                  </p>
                </section>

                <section>
                  <h2 className="text-2xl font-semibold mb-4">10. Contact Information</h2>
                  <p className="text-muted-foreground leading-relaxed">
                    If you have any questions about these Terms of Service, please contact us through our contact page or email us directly.
                  </p>
                </section>
              </CardContent>
            </Card>
          </div>
        </div>
        
        <MinimalFooter />
      </div>
    </>
  );
};

export default Terms;