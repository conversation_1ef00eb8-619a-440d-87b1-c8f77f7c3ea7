import React, { useState, useEffect } from 'react';

interface HeroImageSliderProps {
  className?: string;
}

const HeroImageSlider: React.FC<HeroImageSliderProps> = ({ className = "" }) => {
  const [currentImage, setCurrentImage] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);

  const images = [
    {
      src: "/lovable-uploads/74f9ebe9-3442-443d-ad3c-e5046e67a481.png",
      alt: "ML Sports Engine - AI Analytics"
    },
    {
      src: "/lovable-uploads/e0b3facb-aefb-45c5-ba01-7ac22af5283d.png", 
      alt: "ML Sports Engine - Performance Data"
    },
    {
      src: "/lovable-uploads/8d5d5b01-5635-44c6-a918-9dbbdf52836f.png",
      alt: "ML Sports Team - Mixed Athletes"
    },
    {
      src: "/lovable-uploads/13794c49-81dc-4bc6-bd91-4a5bc13656b5.png",
      alt: "ML Sports Team - Professional Athletes"
    },
    {
      src: "/lovable-uploads/d91de4fd-b7be-44c5-959a-a3e7ab3b1377.png",
      alt: "ML Sports Team - White Kit Athletes"
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setIsTransitioning(true);
      
      setTimeout(() => {
        setCurrentImage(prev => (prev + 1) % images.length);
        setIsTransitioning(false);
      }, 500);
    }, 4000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className={`flex justify-center lg:justify-end ${className}`}>
      <div className="relative max-w-xs">
        {/* Current Image */}
        <div className="relative">
          <img 
            src={images[currentImage].src}
            alt={images[currentImage].alt}
            className={`w-full h-auto object-contain transition-all duration-500 ${
              isTransitioning ? 'opacity-0 scale-95 blur-sm' : 'opacity-100 scale-100 blur-0'
            }`}
          />
          
          {/* Magical transition effects */}
          {isTransitioning && (
            <>
              {/* Particle effect overlay */}
              <div className="absolute inset-0 overflow-hidden">
                {[...Array(12)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-2 h-2 bg-primary rounded-full animate-ping"
                    style={{
                      left: `${Math.random() * 100}%`,
                      top: `${Math.random() * 100}%`,
                      animationDelay: `${Math.random() * 0.5}s`,
                      animationDuration: '1s'
                    }}
                  />
                ))}
              </div>
              
              {/* Energy wave effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/30 to-transparent animate-pulse" />
            </>
          )}
        </div>
        
        {/* Enhanced glowing effect - Removed red backgrounds */}
        <div className="absolute inset-0 bg-muted/20 blur-3xl scale-110 -z-10 animate-pulse"></div>
        <div className="absolute inset-0 bg-muted/10 blur-2xl scale-105 -z-20 animate-pulse"></div>
        
        {/* Magic sparkle effects around the image */}
        <div className="absolute -inset-4">
          {[...Array(8)].map((_, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-muted rounded-full animate-ping"
              style={{
                left: `${10 + (i * 12)}%`,
                top: `${5 + (i % 3) * 30}%`,
                animationDelay: `${i * 0.2}s`,
                animationDuration: '2s'
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default HeroImageSlider;