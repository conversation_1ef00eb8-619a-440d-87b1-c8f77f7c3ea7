import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Smartphone, Download, Apple } from 'lucide-react';

const DownloadAppSection: React.FC = () => {
  return (
    <Card className="bg-gradient-card border border-border/50 mb-8">
      <CardContent className="p-6">
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center">
              <Smartphone className="h-8 w-8 text-primary" />
            </div>
          </div>
          
          <h3 className="text-xl font-bruno tracking-wide mb-2">
            Download Our App
          </h3>
          <p className="text-muted-foreground text-sm mb-6">
            Get instant access to premium predictions on the go
          </p>
          
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button variant="outline" className="flex items-center gap-2">
              <Apple className="h-4 w-4" />
              App Store
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Download className="h-4 w-4" />
              Google Play
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default DownloadAppSection;