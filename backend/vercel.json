{"version": 2, "builds": [{"src": "src/server.ts", "use": "@vercel/node", "config": {"includeFiles": ["prisma/**", "dist/**"], "maxLambdaSize": "50mb"}}], "routes": [{"src": "/(.*)", "dest": "src/server.ts", "headers": {"Access-Control-Allow-Origin": "*", "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS", "Access-Control-Allow-Headers": "Content-Type, Authorization, X-Requested-With"}}], "env": {"NODE_ENV": "production", "VERCEL": "1"}}