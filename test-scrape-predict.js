// Test script for the scrape-and-predict edge function
const SUPABASE_URL = 'https://bylrsugfuksgjbhpjgxh.supabase.co';
const FUNCTION_URL = `${SUPABASE_URL}/functions/v1/scrape-and-predict`;

// Test data - using mock URLs for demonstration
const testRequest = {
  urls: [
    "https://www.espn.com/nfl/schedule",
    "https://www.nfl.com/schedules"
  ],
  sport: "football",
  league: "NFL",
  auto_predict: true,
  prediction_markets: ["Moneyline", "Spread"],
  scrape_config: {
    selectors: {
      matches: ".game, .match-item, .event-row",
      teams: ".team-name, .competitor",
      odds: ".odds, .line",
      dates: ".date, .time"
    },
    filters: {
      min_confidence: 65,
      max_matches: 20
    }
  }
};

async function testScrapeAndPredict() {
  try {
    console.log('Testing scrape-and-predict edge function...');
    console.log('Request:', JSON.stringify(testRequest, null, 2));
    
    const response = await fetch(FUNCTION_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer YOUR_JWT_TOKEN_HERE` // Replace with actual admin token
      },
      body: JSON.stringify(testRequest)
    });

    const result = await response.json();
    
    console.log('\nResponse Status:', response.status);
    console.log('Response:', JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('\n✅ Test successful!');
      console.log(`📊 Scraped ${result.scraped_matches} matches`);
      console.log(`💾 Saved ${result.saved_matches} matches to database`);
      console.log(`🎯 Generated ${result.predictions_generated} predictions`);
      console.log(`💾 Saved ${result.saved_predictions} predictions to database`);
    } else {
      console.log('\n❌ Test failed:', result.error);
    }
    
  } catch (error) {
    console.error('❌ Test error:', error.message);
  }
}

// Test with minimal request (no URLs, just prediction generation)
async function testPredictionOnly() {
  const minimalRequest = {
    sport: "football",
    league: "NFL",
    auto_predict: true
  };
  
  try {
    console.log('\n\nTesting prediction-only mode...');
    
    const response = await fetch(FUNCTION_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer YOUR_JWT_TOKEN_HERE` // Replace with actual admin token
      },
      body: JSON.stringify(minimalRequest)
    });

    const result = await response.json();
    
    console.log('Prediction-only Response:', JSON.stringify(result, null, 2));
    
  } catch (error) {
    console.error('❌ Prediction test error:', error.message);
  }
}

// Run tests
console.log('🚀 Starting edge function tests...\n');
testScrapeAndPredict().then(() => {
  return testPredictionOnly();
}).then(() => {
  console.log('\n✅ All tests completed!');
}).catch(error => {
  console.error('❌ Test suite failed:', error);
});
