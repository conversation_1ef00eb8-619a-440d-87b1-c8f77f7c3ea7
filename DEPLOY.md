Deployment guide: Scraping + ML + Supabase

1) Deploy Python service
- Build container
  docker build -t sports-ml:latest -f python_service/Dockerfile .
- Run locally
  docker run -p 8088:8088 --env-file python_service/.env sports-ml:latest
- Or run via uvicorn
  cd python_service && uvicorn app.main:app --host 0.0.0.0 --port 8088

2) Configure Supabase secrets
- In Supabase Dashboard > Project Settings > Configuration > Secret Store, set:
  - SUPABASE_URL (already present)
  - SUPABASE_SERVICE_ROLE_KEY (already present)
  - PY_SERVICE_URL (e.g., https://your-python-host:8088)
  - ADMIN_GRANT_SECRET (random string, used by grant-admin function)

3) Create database schema
- Open Supabase SQL editor and run:
  supabase/sql/initial_schema.sql

4) Deploy Edge Functions
- Using Supabase CLI:
  supabase functions deploy enhanced-scraper-cron
  supabase functions deploy generate-predictions
  supabase functions deploy sports-data-api
  supabase functions deploy manage-users
  supabase functions deploy paystack-payment flutterwave-payment
  supabase functions deploy grant-admin
  supabase functions deploy scraperapi-proxy
  supabase functions deploy admin-trigger-scrape
  supabase functions deploy ping-python
- Optionally serve locally for testing:
  supabase functions serve generate-predictions

5) Scheduling (cron)
- In Supabase Dashboard > Edge Functions > Schedules, create a schedule for enhanced-scraper-cron (e.g., every hour).
- Ensure PY_SERVICE_URL is reachable from Supabase Edge.

6) Quick verification
- Python service health:
  curl $PY_SERVICE_URL/health
- Trigger scraper cron (manual):
  curl -X POST https://<project>.functions.supabase.co/enhanced-scraper-cron
- Generate predictions on demand:
  curl -X POST https://<project>.functions.supabase.co/generate-predictions \
       -H "Authorization: Bearer <user_access_token>" \
       -H "Content-Type: application/json" -d '{"sport":"football"}'
- Frontend: npm run dev and ensure predictions list populates via sports-data-api

7) Notes
- Service role bypasses RLS; client reads live_predictions directly per the basic policy in initial_schema.sql. Harden policies for production.
- For JS-heavy sources, prefer calling Python /scrape with use_js true in a dedicated Edge function or from the cron.
