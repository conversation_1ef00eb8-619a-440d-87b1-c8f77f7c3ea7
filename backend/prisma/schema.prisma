// SQLite-compatible Prisma schema
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id                   String    @id @default(cuid())
  email                String    @unique
  password             String
  fullName             String?   @map("full_name")
  role                 String    @default("USER")
  subscriptionTier     String    @default("FREE") @map("subscription_tier")
  subscriptionStatus   String    @default("INACTIVE") @map("subscription_status")
  subscriptionEnd      DateTime? @map("subscription_end")
  stripeCustomerId     String?   @map("stripe_customer_id")
  emailVerified        Boolean   @default(false) @map("email_verified")
  emailVerificationToken String? @map("email_verification_token")
  passwordResetToken   String?   @map("password_reset_token")
  passwordResetExpires DateTime? @map("password_reset_expires")
  lastLogin            DateTime? @map("last_login")
  createdAt            DateTime  @default(now()) @map("created_at")
  updatedAt            DateTime  @updatedAt @map("updated_at")

  // Relations
  predictions          Prediction[]
  userRewards          UserReward[]
  auditLogs            AuditLog[]

  @@map("users")
}

model SportsData {
  id          String    @id @default(cuid())
  sport       String
  league      String
  homeTeam    String    @map("home_team")
  awayTeam    String    @map("away_team")
  homeScore   Int?      @map("home_score")
  awayScore   Int?      @map("away_score")
  status      String    @default("UPCOMING")
  matchDate   DateTime  @map("match_date")
  odds        String?   // JSON as string
  stats       String?   // JSON as string
  source      String
  externalId  String?   @map("external_id")
  venue       String?
  weather     String?
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  // Relations
  predictions Prediction[]

  @@map("sports_data")
}

model Prediction {
  id             String    @id @default(cuid())
  sport          String
  league         String
  homeTeam       String    @map("home_team")
  awayTeam       String    @map("away_team")
  matchDate      DateTime  @map("match_date")
  market         String
  pick           String
  odds           Float
  confidence     Float
  source         String    @default("ML")
  status         String    @default("UPCOMING")
  result         String?
  isPremium      Boolean   @default(false) @map("is_premium")
  isPublished    Boolean   @default(true) @map("is_published")
  expectedValue  Float?    @map("expected_value")
  factors        String?   // JSON as string
  analysis       String?
  userId         String?   @map("user_id")
  sportsDataId   String?   @map("sports_data_id")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")

  // Relations
  user           User?      @relation(fields: [userId], references: [id])
  sportsData     SportsData? @relation(fields: [sportsDataId], references: [id])

  @@map("predictions")
}

model UserReward {
  id          String    @id @default(cuid())
  userId      String    @map("user_id")
  type        String    @default("CREDITS")
  amount      Float
  description String?
  isRedeemed  Boolean   @default(false) @map("is_redeemed")
  redeemedAt  DateTime? @map("redeemed_at")
  expiresAt   DateTime? @map("expires_at")
  createdAt   DateTime  @default(now()) @map("created_at")

  // Relations
  user        User      @relation(fields: [userId], references: [id])

  @@map("user_rewards")
}

model AuditLog {
  id        String    @id @default(cuid())
  userId    String?   @map("user_id")
  action    String
  resource  String
  details   String?   // JSON as string
  ipAddress String?   @map("ip_address")
  userAgent String?   @map("user_agent")
  createdAt DateTime  @default(now()) @map("created_at")

  // Relations
  user      User?     @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

model GlobalSettings {
  key       String    @id
  value     String    // JSON as string
  updatedAt DateTime  @updatedAt @map("updated_at")
  updatedBy String?   @map("updated_by")

  @@map("global_settings")
}

model ScrapingJob {
  id          String    @id @default(cuid())
  provider    String
  sport       String
  league      String?
  status      String    @default("PENDING")
  startedAt   DateTime? @map("started_at")
  completedAt DateTime? @map("completed_at")
  error       String?
  results     String?   // JSON as string
  createdAt   DateTime  @default(now()) @map("created_at")

  @@map("scraping_jobs")
}
