import React from 'react';
import { Head<PERSON> } from '@/components/Header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { BarChart3, TrendingUp, Users, Target, Calendar, Download } from 'lucide-react';

const AdminAnalytics: React.FC = () => {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">Analytics Dashboard</h1>
          <p className="text-muted-foreground">Monitor platform performance and user engagement</p>
        </div>

        <div className="grid gap-6">
          <Card className="border-border shadow-carved-deep bg-card">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Performance Metrics
                </div>
                <Button variant="outline">
                  <Download className="h-4 w-4 mr-2" />
                  Export Data
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="text-center p-4 border border-border rounded-lg shadow-carved bg-card">
                  <TrendingUp className="h-8 w-8 mx-auto mb-2 text-success" />
                  <div className="text-2xl font-bold text-primary">94.2%</div>
                  <div className="text-sm text-muted-foreground">Prediction Accuracy</div>
                </div>
                <div className="text-center p-4 border border-border rounded-lg shadow-carved bg-card">
                  <Users className="h-8 w-8 mx-auto mb-2 text-info" />
                  <div className="text-2xl font-bold text-primary">8,234</div>
                  <div className="text-sm text-muted-foreground">Daily Active Users</div>
                </div>
                <div className="text-center p-4 border border-border rounded-lg shadow-carved bg-card">
                  <Target className="h-8 w-8 mx-auto mb-2 text-warning" />
                  <div className="text-2xl font-bold text-primary">1,247</div>
                  <div className="text-sm text-muted-foreground">Predictions Today</div>
                </div>
                <div className="text-center p-4 border border-border rounded-lg shadow-carved bg-card">
                  <Calendar className="h-8 w-8 mx-auto mb-2 text-primary" />
                  <div className="text-2xl font-bold text-primary">156</div>
                  <div className="text-sm text-muted-foreground">Active Leagues</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="border-border shadow-carved-deep bg-card">
              <CardHeader>
                <CardTitle>User Engagement</CardTitle>
                <CardDescription>Weekly user activity trends</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Login Rate</span>
                    <span className="text-sm font-medium">+12%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Session Duration</span>
                    <span className="text-sm font-medium">8.4 min</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Bounce Rate</span>
                    <span className="text-sm font-medium">23.5%</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-muted-foreground">Conversion Rate</span>
                    <span className="text-sm font-medium">4.2%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-border shadow-carved-deep bg-card">
              <CardHeader>
                <CardTitle>Revenue Analytics</CardTitle>
                <CardDescription>Financial performance overview</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center p-4 border border-border rounded-lg shadow-carved bg-card">
                    <div className="text-2xl font-bold text-success">$45,678</div>
                    <div className="text-sm text-muted-foreground">Monthly Revenue</div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 border border-border rounded-lg shadow-carved bg-card">
                      <div className="font-bold text-primary">$1,234</div>
                      <div className="text-xs text-muted-foreground">Daily Average</div>
                    </div>
                    <div className="text-center p-3 border border-border rounded-lg shadow-carved bg-card">
                      <div className="font-bold text-primary">2,156</div>
                      <div className="text-xs text-muted-foreground">Premium Users</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminAnalytics;