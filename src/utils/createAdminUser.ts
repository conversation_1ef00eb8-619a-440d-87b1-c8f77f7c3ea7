import { apiService } from '@/services/api';

export const createAdminUser = async (email: string, password: string, fullName?: string, role: 'admin' | 'super_admin' = 'admin') => {
  try {
    const response = await apiService.register({
      email,
      password,
      fullName,
      role
    });

    if (!response.success) {
      throw new Error(response.error);
    }

    return { success: true, data: response.data };
  } catch (error) {
    console.error('Error creating admin user:', error);
    return { success: false, error: error.message };
  }
};

// Create the specific admin user requested
export const createSpecificAdminUser = async () => {
  return await createAdminUser(
    '<EMAIL>',
    'Americana123456789@',
    'Admin User',
    'admin'
  );
};