import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';

import { errorHandler } from '@/middleware/errorHandler';
import { notFoundHandler } from '@/middleware/notFoundHandler';
import { logger } from '@/utils/logger';
import { connectDatabase } from '@/config/database';

// Import routes
import authRoutes from '@/routes/auth';
import userRoutes from '@/routes/users';
import sportsDataRoutes from '@/routes/sportsData';
import predictionRoutes from '@/routes/predictions';
import scrapingRoutes from '@/routes/scraping';
import paymentRoutes from '@/routes/payments';
import automationRoutes from '@/routes/automation';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

// Manual CORS middleware (fallback)
app.use((req, res, next) => {
  const allowedOrigins = [
    'http://localhost:8080',
    'http://localhost:5173',
    'https://1300blk.online',
    'https://www.1300blk.online',
    'https://onethreezerozeroblk-77-hgy2lop26-bmds-projects-6efc3abf.vercel.app',
    'https://onethreezerozeroblk-77-6hi10d9o3-bmds-projects-6efc3abf.vercel.app',
    'https://onethreezerozeroblk-77-7ea3ribwi-bmds-projects-6efc3abf.vercel.app',
    'https://onethreezerozeroblk-77-7h55fvjdq-bmds-projects-6efc3abf.vercel.app'
  ];

  const origin = req.headers.origin;
  if (allowedOrigins.includes(origin as string)) {
    res.setHeader('Access-Control-Allow-Origin', origin as string);
  } else {
    res.setHeader('Access-Control-Allow-Origin', '*');
  }

  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
  res.setHeader('Access-Control-Allow-Credentials', 'true');

  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  next();
});

// Middleware
app.use(helmet());
app.use(compression());
app.use(limiter);
app.use(cors({
  origin: [
    'http://localhost:8080',
    'http://localhost:5173',
    'https://1300blk.online',
    'https://www.1300blk.online',
    'https://onethreezerozeroblk-77-hgy2lop26-bmds-projects-6efc3abf.vercel.app',
    'https://onethreezerozeroblk-77-6hi10d9o3-bmds-projects-6efc3abf.vercel.app',
    'https://onethreezerozeroblk-77-7ea3ribwi-bmds-projects-6efc3abf.vercel.app',
    'https://onethreezerozeroblk-77-7h55fvjdq-bmds-projects-6efc3abf.vercel.app',
    ...(process.env.CORS_ORIGIN ? [process.env.CORS_ORIGIN] : [])
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}));
app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()) } }));
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/users', userRoutes);
app.use('/api/sports-data', sportsDataRoutes);
app.use('/api/predictions', predictionRoutes);
app.use('/api/scraping', scrapingRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/automation', automationRoutes);

// Error handling middleware
app.use(notFoundHandler);
app.use(errorHandler);

// Start server
async function startServer() {
  try {
    // Connect to database
    await connectDatabase();

    // Initialize automation system
    const { scrapingScheduler } = await import('@/services/automation/scrapingScheduler');
    await scrapingScheduler.initialize();
    logger.info('🤖 Automation system initialized');

    app.listen(PORT, () => {
      logger.info(`🚀 Server running on port ${PORT}`);
      logger.info(`📊 Environment: ${process.env.NODE_ENV}`);
      logger.info(`🔗 Health check: http://localhost:${PORT}/health`);
    });
  } catch (error) {
    logger.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

startServer();

export default app;
