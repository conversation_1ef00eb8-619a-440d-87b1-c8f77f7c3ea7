import { useState, useEffect } from "react";
import { Header } from "@/components/Header";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from "@/components/ui/breadcrumb";
import { SportTypeSelector } from "@/components/SportTypeSelector";
import { leagues } from "@/components/LeagueSelector";
import { apiService } from "@/services/api";
import { Search, Trophy, Globe, Star, TrendingUp, Users, Calendar } from "lucide-react";
import { Link } from "react-router-dom";

const Leagues = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedSport, setSelectedSport] = useState('');
  const [leagueStats, setLeagueStats] = useState<Record<string, any>>({});

  const filteredLeagues = leagues.filter(league => {
    const matchesSearch = league.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         league.country.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesSport = !selectedSport || league.sport === selectedSport;
    return matchesSearch && matchesSport;
  });

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'premium':
        return <Star className="h-4 w-4 text-warning" />;
      case 'major':
        return <Trophy className="h-4 w-4 text-info" />;
      default:
        return <Globe className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getTierBadge = (tier: string) => {
    switch (tier) {
      case 'premium':
        return <Badge className="bg-warning text-warning-foreground">Premium</Badge>;
      case 'major':
        return <Badge className="bg-info text-info-foreground">Major</Badge>;
      default:
        return <Badge variant="secondary">Minor</Badge>;
    }
  };

  useEffect(() => {
    const loadLeagueStats = async () => {
      const stats: Record<string, any> = {};
      for (const league of leagues) {
        stats[league.id] = await getLeagueStats(league.id);
      }
      setLeagueStats(stats);
    };
    
    loadLeagueStats();
  }, []);

  const getLeagueStats = async (leagueId: string) => {
    try {
      const { data, error } = await apiService.getTodaysPredictions({ league: leagueId, limit: 100 });
      if (error || !data) throw new Error('Failed to fetch league stats');
      
      return {
        teams: Math.floor(Math.random() * 20) + 16, // TODO: Get real team count
        predictions: data.length,
        accuracy: Math.floor(Math.random() * 15) + 75, // TODO: Calculate real accuracy
        upcoming: data.filter(p => p.status === 'upcoming').length
      };
    } catch (error) {
      // Fallback to mock stats if API fails
      return {
        teams: Math.floor(Math.random() * 20) + 16,
        predictions: Math.floor(Math.random() * 100) + 50,
        accuracy: Math.floor(Math.random() * 15) + 75,
        upcoming: Math.floor(Math.random() * 10) + 5
      };
    }
  };

  return (
    <div className="min-h-screen bg-gradient-hero">
      <Header />
      
      <main className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <Breadcrumb className="mb-6">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link to="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Leagues</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>

        {/* Hero Section */}
        <section className="mb-8">
          <Card className="bg-gradient-card shadow-glow relative overflow-hidden">
            {/* Background Chart Stats */}
            <div className="absolute inset-0 opacity-10">
              <svg width="100%" height="100%" viewBox="0 0 800 250" className="absolute inset-0">
                <defs>
                  <linearGradient id="leaguesChartGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor="hsl(var(--primary))" />
                    <stop offset="100%" stopColor="hsl(var(--accent))" />
                  </linearGradient>
                </defs>
                {/* Chart network */}
                <circle cx="150" cy="120" r="30" fill="none" stroke="url(#leaguesChartGradient)" strokeWidth="2" opacity="0.7" />
                <circle cx="300" cy="80" r="25" fill="none" stroke="url(#leaguesChartGradient)" strokeWidth="2" opacity="0.7" />
                <circle cx="450" cy="140" r="35" fill="none" stroke="url(#leaguesChartGradient)" strokeWidth="2" opacity="0.7" />
                <circle cx="600" cy="100" r="20" fill="none" stroke="url(#leaguesChartGradient)" strokeWidth="2" opacity="0.7" />
                {/* Connecting lines */}
                <line x1="180" y1="120" x2="275" y2="80" stroke="hsl(var(--primary))" strokeWidth="1" opacity="0.5" />
                <line x1="325" y1="80" x2="415" y2="140" stroke="hsl(var(--primary))" strokeWidth="1" opacity="0.5" />
                <line x1="485" y1="140" x2="580" y2="100" stroke="hsl(var(--primary))" strokeWidth="1" opacity="0.5" />
                {/* Data points */}
                <circle cx="150" cy="120" r="3" fill="hsl(var(--primary))" />
                <circle cx="300" cy="80" r="3" fill="hsl(var(--primary))" />
                <circle cx="450" cy="140" r="3" fill="hsl(var(--primary))" />
                <circle cx="600" cy="100" r="3" fill="hsl(var(--primary))" />
              </svg>
            </div>
            
            <div className="p-6 md:p-8 relative z-10">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 items-center">
                {/* Text Content */}
                <div className="lg:col-span-2">
                  <h1 className="text-3xl md:text-5xl font-bebas tracking-wide text-foreground mb-4">
                    ELITE SPORTS
                    <br />
                    <span className="text-primary">LEAGUES</span>
                  </h1>
                  <p className="text-muted-foreground text-lg mb-4">
                    Comprehensive coverage of major sports leagues worldwide. Get detailed 
                    statistics, predictions, and insights for every competition.
                  </p>
                </div>
                
                {/* Hero Image */}
                <div className="flex justify-center lg:justify-end">
                  <div className="relative">
                    <img 
                      src="/lovable-uploads/74f9ebe9-3442-443d-ad3c-e5046e67a481.png" 
                      alt="ML Sports Elite Athlete" 
                      className="w-full max-w-xs md:max-w-sm h-auto object-contain"
                    />
                    {/* Glowing effect behind image */}
                    <div className="absolute inset-0 bg-gradient-primary opacity-20 blur-3xl scale-110 -z-10"></div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </section>

        {/* Search and Filter */}
        <Card className="mb-8 bg-gradient-card">
          <CardContent className="pt-6">
            <div className="space-y-4">
              {/* Search */}
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search leagues or countries..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* Sport Filter */}
              <div>
                <label className="text-sm font-medium mb-2 block">Filter by Sport</label>
                <SportTypeSelector
                  selectedSport={selectedSport}
                  onSportChange={setSelectedSport}
                  showBadges={true}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card className="bg-gradient-card text-center">
            <CardContent className="pt-6">
              <Trophy className="h-8 w-8 mx-auto mb-2 text-primary" />
              <div className="text-2xl font-bold">{filteredLeagues.length}</div>
              <div className="text-sm text-muted-foreground">Total Leagues</div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card text-center">
            <CardContent className="pt-6">
              <Star className="h-8 w-8 mx-auto mb-2 text-warning" />
              <div className="text-2xl font-bold">
                {filteredLeagues.filter(l => l.tier === 'premium').length}
              </div>
              <div className="text-sm text-muted-foreground">Premium</div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card text-center">
            <CardContent className="pt-6">
              <Globe className="h-8 w-8 mx-auto mb-2 text-info" />
              <div className="text-2xl font-bold">
                {new Set(filteredLeagues.map(l => l.country)).size}
              </div>
              <div className="text-sm text-muted-foreground">Countries</div>
            </CardContent>
          </Card>
          <Card className="bg-gradient-card text-center">
            <CardContent className="pt-6">
              <TrendingUp className="h-8 w-8 mx-auto mb-2 text-success" />
              <div className="text-2xl font-bold">85%</div>
              <div className="text-sm text-muted-foreground">Avg Accuracy</div>
            </CardContent>
          </Card>
        </div>

        {/* Leagues Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredLeagues.map((league) => {
            const stats = leagueStats[league.id] || { teams: 0, predictions: 0, accuracy: 0, upcoming: 0 };
            return (
              <Card key={league.id} className="bg-gradient-card hover:shadow-glow transition-all duration-300">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="font-bebas tracking-wide mb-2">
                        {league.name}
                      </CardTitle>
                      <CardDescription className="flex items-center gap-2">
                        <Globe className="h-3 w-3" />
                        {league.country}
                      </CardDescription>
                    </div>
                    <div className="flex flex-col items-end gap-2">
                      {getTierBadge(league.tier)}
                      <Badge variant="outline" className="text-xs capitalize">
                        {league.sport}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* League Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-lg font-bold text-primary">{stats.teams}</div>
                      <div className="text-xs text-muted-foreground">Teams</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-success">{stats.predictions}</div>
                      <div className="text-xs text-muted-foreground">Predictions</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-warning">{stats.accuracy}%</div>
                      <div className="text-xs text-muted-foreground">Accuracy</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-info">{stats.upcoming}</div>
                      <div className="text-xs text-muted-foreground">Upcoming</div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2">
                    <Link to={`/predictions?league=${league.id}`} className="flex-1">
                      <Badge className="w-full justify-center cursor-pointer hover:bg-primary/80">
                        View Predictions
                      </Badge>
                    </Link>
                    <Badge variant="outline" className="cursor-pointer hover:bg-muted">
                      {getTierIcon(league.tier)}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* About Section */}
        <Card className="mt-12 bg-gradient-card">
          <CardHeader>
            <CardTitle className="font-bebas tracking-wide text-center">League Coverage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
              <div>
                <Star className="h-8 w-8 mx-auto mb-3 text-warning" />
                <h3 className="font-semibold mb-2">Premium Leagues</h3>
                <p className="text-sm text-muted-foreground">
                  Top-tier competitions with highest prediction accuracy and detailed analysis
                </p>
              </div>
              <div>
                <Trophy className="h-8 w-8 mx-auto mb-3 text-info" />
                <h3 className="font-semibold mb-2">Major Leagues</h3>
                <p className="text-sm text-muted-foreground">
                  Important regional and international competitions with quality coverage
                </p>
              </div>
              <div>
                <Globe className="h-8 w-8 mx-auto mb-3 text-muted-foreground" />
                <h3 className="font-semibold mb-2">Minor Leagues</h3>
                <p className="text-sm text-muted-foreground">
                  Emerging leagues and smaller competitions for comprehensive coverage
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </main>
    </div>
  );
};

export default Leagues;