
import { useState, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { apiService } from '@/services/api'

interface SubscriptionData {
  subscribed: boolean
  subscription_tier: string | null
  subscription_end: string | null
}

export const useSubscription = () => {
  const { user } = useAuth()
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData>({
    subscribed: false,
    subscription_tier: null,
    subscription_end: null
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const checkSubscription = async () => {
    if (!user) return

    setLoading(true)
    setError(null)
    
    try {
      // Check subscription from database or implement your payment provider logic here
      const response = await apiService.getUserProfile()

      if (!response.success) throw new Error(response.error)

      if (response.data) {
        setSubscriptionData({
          subscribed: response.data.subscription_status === 'active',
          subscription_tier: response.data.subscription_tier,
          subscription_end: response.data.subscription_end
        })
      }
    } catch (err) {
      console.error('Error checking subscription:', err)
      setError(err instanceof Error ? err.message : 'Failed to check subscription')
    } finally {
      setLoading(false)
    }
  }

  const createCheckout = async (plan: string = 'premium') => {
    if (!user) throw new Error('User not authenticated')
    
    // Implement your payment provider checkout logic here
    throw new Error('Payment provider not configured')
  }

  const openCustomerPortal = async () => {
    if (!user) throw new Error('User not authenticated')
    
    // Implement your payment provider customer portal logic here
    throw new Error('Payment provider not configured')
  }

  useEffect(() => {
    if (user) {
      checkSubscription()
    }
  }, [user])

  return {
    ...subscriptionData,
    loading,
    error,
    checkSubscription,
    createCheckout,
    openCustomerPortal,
  }
}
