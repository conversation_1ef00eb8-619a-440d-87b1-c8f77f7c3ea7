
import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { apiService } from '@/services/api'
import { toast } from 'sonner'

interface FlutterwavePayment {
  amount: number
  currency: string
  plan: string
  customer: {
    email: string
    name: string
  }
}

interface PaystackPayment {
  amount: number
  email: string
  plan: string
  currency: string
}

export const useCryptoPayments = () => {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)

  const createFlutterwavePayment = async (paymentData: FlutterwavePayment) => {
    if (!user) throw new Error('User not authenticated')

    setLoading(true)
    try {
      const response = await apiService.createFlutterwavePayment(paymentData)

      if (!response.success) throw new Error(response.error)
      return response.data.payment_link
    } catch (error) {
      console.error('Flutterwave payment error:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const createPaystackPayment = async (paymentData: PaystackPayment) => {
    if (!user) throw new Error('User not authenticated')

    setLoading(true)
    try {
      const response = await apiService.createPaystackPayment(paymentData)

      if (!response.success) throw new Error(response.error)
      return response.data.authorization_url
    } catch (error) {
      console.error('Paystack payment error:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const initiateCryptoPayment = async (provider: 'bitcoin' | 'ethereum', amount: number, plan: string) => {
    if (!user) throw new Error('User not authenticated')

    setLoading(true)
    try {
      const response = await apiService.createCryptoPayment({
        provider,
        amount,
        plan,
        user_id: user.id
      })

      if (!response.success) throw new Error(response.error)
      return response.data
    } catch (error) {
      console.error('Crypto payment error:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  return {
    loading,
    createFlutterwavePayment,
    createPaystackPayment,
    initiateCryptoPayment
  }
}
