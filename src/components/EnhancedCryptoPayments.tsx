import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Bitcoin, Wallet, CreditCard, Loader2, QrCode, Copy, CheckCircle } from 'lucide-react';
import { useCryptoPayments } from '@/hooks/useCryptoPayments';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'sonner';

interface PaymentPlan {
  name: string;
  price: number;
  tier: string;
  duration: string;
  features: string[];
}

const PAYMENT_PLANS: PaymentPlan[] = [
  {
    name: 'Basic Monthly',
    price: 29,
    tier: 'basic',
    duration: '1 month',
    features: ['Daily predictions', 'Basic analytics', 'Email support']
  },
  {
    name: 'Premium Monthly',
    price: 49,
    tier: 'premium',
    duration: '1 month',
    features: ['All basic features', 'Premium predictions', 'Advanced analytics', 'Priority support']
  },
  {
    name: 'Premium Yearly',
    price: 499,
    tier: 'premium_yearly',
    duration: '12 months',
    features: ['All premium features', '2 months free', 'VIP support', 'Early access to new features']
  }
];

interface EnhancedCryptoPaymentsProps {
  selectedPlan?: PaymentPlan;
  onPaymentSuccess?: () => void;
}

const EnhancedCryptoPayments: React.FC<EnhancedCryptoPaymentsProps> = ({
  selectedPlan,
  onPaymentSuccess
}) => {
  const { user } = useAuth();
  const { loading, createFlutterwavePayment, createPaystackPayment, initiateCryptoPayment } = useCryptoPayments();
  const [activeProvider, setActiveProvider] = useState<string | null>(null);
  const [selectedPlanState, setSelectedPlanState] = useState<PaymentPlan>(selectedPlan || PAYMENT_PLANS[1]);
  const [cryptoPaymentDetails, setCryptoPaymentDetails] = useState<any>(null);
  const [paymentStep, setPaymentStep] = useState<'select' | 'processing' | 'crypto_details'>('select');

  const handleFlutterwavePayment = async () => {
    try {
      setActiveProvider('flutterwave');
      setPaymentStep('processing');
      
      const paymentLink = await createFlutterwavePayment({
        amount: selectedPlanState.price,
        currency: 'USD',
        plan: selectedPlanState.tier,
        customer: {
          email: user?.email || '',
          name: user?.user_metadata?.full_name || 'User'
        }
      });
      
      // Open payment in new window
      const paymentWindow = window.open(paymentLink, '_blank', 'width=600,height=800');
      
      // Monitor payment window
      const checkClosed = setInterval(() => {
        if (paymentWindow?.closed) {
          clearInterval(checkClosed);
          setPaymentStep('select');
          setActiveProvider(null);
          toast.info('Payment window closed. If payment was completed, it may take a few minutes to reflect.');
          onPaymentSuccess?.();
        }
      }, 1000);
      
    } catch (error) {
      toast.error('Failed to initialize Flutterwave payment');
      setPaymentStep('select');
      setActiveProvider(null);
    }
  };

  const handlePaystackPayment = async () => {
    try {
      setActiveProvider('paystack');
      setPaymentStep('processing');
      
      const authUrl = await createPaystackPayment({
        amount: selectedPlanState.price * 100, // Paystack expects amount in kobo
        email: user?.email || '',
        plan: selectedPlanState.tier,
        currency: 'USD'
      });
      
      // Open payment in new window
      const paymentWindow = window.open(authUrl, '_blank', 'width=600,height=800');
      
      // Monitor payment window
      const checkClosed = setInterval(() => {
        if (paymentWindow?.closed) {
          clearInterval(checkClosed);
          setPaymentStep('select');
          setActiveProvider(null);
          toast.info('Payment window closed. If payment was completed, it may take a few minutes to reflect.');
          onPaymentSuccess?.();
        }
      }, 1000);
      
    } catch (error) {
      toast.error('Failed to initialize Paystack payment');
      setPaymentStep('select');
      setActiveProvider(null);
    }
  };

  const handleCryptoPayment = async (provider: 'bitcoin' | 'ethereum') => {
    try {
      setActiveProvider(provider);
      setPaymentStep('processing');
      
      const result = await initiateCryptoPayment(provider, selectedPlanState.price, selectedPlanState.tier);
      
      setCryptoPaymentDetails(result);
      setPaymentStep('crypto_details');
      
      toast.success(`${provider} payment initiated. Please complete the payment using the details below.`);
      
    } catch (error) {
      toast.error(`Failed to initialize ${provider} payment`);
      setPaymentStep('select');
      setActiveProvider(null);
    }
  };

  const copyToClipboard = (text: string, label: string) => {
    navigator.clipboard.writeText(text);
    toast.success(`${label} copied to clipboard!`);
  };

  const confirmCryptoPayment = () => {
    toast.success('Payment confirmation submitted! We will verify your transaction within 24 hours.');
    setPaymentStep('select');
    setActiveProvider(null);
    setCryptoPaymentDetails(null);
    onPaymentSuccess?.();
  };

  if (paymentStep === 'crypto_details' && cryptoPaymentDetails) {
    return (
      <Card className="max-w-md mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {activeProvider === 'bitcoin' ? <Bitcoin className="h-5 w-5" /> : <Wallet className="h-5 w-5" />}
            Complete {activeProvider === 'bitcoin' ? 'Bitcoin' : 'Ethereum'} Payment
          </CardTitle>
          <CardDescription>
            Send the exact amount to the address below
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-muted/50 p-4 rounded-lg space-y-3">
            <div>
              <label className="text-sm font-medium">Amount</label>
              <div className="flex items-center gap-2 mt-1">
                <Input 
                  value={`${cryptoPaymentDetails.amount} ${cryptoPaymentDetails.currency}`}
                  readOnly 
                  className="font-mono"
                />
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => copyToClipboard(cryptoPaymentDetails.amount, 'Amount')}
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium">Wallet Address</label>
              <div className="flex items-center gap-2 mt-1">
                <Input 
                  value={cryptoPaymentDetails.address}
                  readOnly 
                  className="font-mono text-xs"
                />
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => copyToClipboard(cryptoPaymentDetails.address, 'Address')}
                >
                  <Copy className="h-3 w-3" />
                </Button>
              </div>
            </div>

            {cryptoPaymentDetails.qr_code && (
              <div className="text-center">
                <div className="bg-white p-2 rounded-lg inline-block">
                  <QrCode className="h-32 w-32 mx-auto" />
                </div>
                <p className="text-xs text-muted-foreground mt-2">QR Code for easy scanning</p>
              </div>
            )}
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg">
            <p className="text-sm text-yellow-800 dark:text-yellow-200">
              <strong>Important:</strong> Send the exact amount to the address above. 
              Your subscription will be activated within 24 hours after confirmation.
            </p>
          </div>

          <div className="flex gap-2">
            <Button 
              onClick={() => setPaymentStep('select')} 
              variant="outline" 
              className="flex-1"
            >
              Back
            </Button>
            <Button 
              onClick={confirmCryptoPayment} 
              className="flex-1"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Payment Sent
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Plan Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Select Plan</CardTitle>
          <CardDescription>Choose the plan that best fits your needs</CardDescription>
        </CardHeader>
        <CardContent>
          <Select 
            value={selectedPlanState.tier} 
            onValueChange={(value) => {
              const plan = PAYMENT_PLANS.find(p => p.tier === value);
              if (plan) setSelectedPlanState(plan);
            }}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {PAYMENT_PLANS.map((plan) => (
                <SelectItem key={plan.tier} value={plan.tier}>
                  {plan.name} - ${plan.price} ({plan.duration})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <div className="mt-4 p-4 bg-muted/50 rounded-lg">
            <h4 className="font-semibold mb-2">{selectedPlanState.name}</h4>
            <p className="text-2xl font-bold text-primary mb-2">${selectedPlanState.price}</p>
            <p className="text-sm text-muted-foreground mb-3">per {selectedPlanState.duration}</p>
            <ul className="space-y-1 text-sm">
              {selectedPlanState.features.map((feature, index) => (
                <li key={index} className="flex items-center gap-2">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                  {feature}
                </li>
              ))}
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Payment Methods */}
      <div className="grid gap-4">
        <h3 className="text-lg font-semibold">Choose Payment Method</h3>
        
        {/* Traditional Payment Methods */}
        <div className="grid md:grid-cols-2 gap-4">
          <Card className="cursor-pointer hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <CreditCard className="h-6 w-6 text-primary" />
                  <div>
                    <CardTitle className="text-base">Flutterwave</CardTitle>
                    <CardDescription className="text-sm">Cards, bank transfer, mobile money</CardDescription>
                  </div>
                </div>
                <Badge variant="secondary" className="bg-primary/20 text-primary">Popular</Badge>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <Button
                onClick={handleFlutterwavePayment}
                disabled={loading || paymentStep === 'processing'}
                className="w-full"
              >
                {activeProvider === 'flutterwave' && paymentStep === 'processing' ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <CreditCard className="h-4 w-4 mr-2" />
                )}
                Pay ${selectedPlanState.price} with Flutterwave
              </Button>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <CreditCard className="h-6 w-6 text-primary" />
                  <div>
                    <CardTitle className="text-base">Paystack</CardTitle>
                    <CardDescription className="text-sm">Secure card and bank payments</CardDescription>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <Button
                onClick={handlePaystackPayment}
                disabled={loading || paymentStep === 'processing'}
                className="w-full"
                variant="outline"
              >
                {activeProvider === 'paystack' && paymentStep === 'processing' ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <CreditCard className="h-4 w-4 mr-2" />
                )}
                Pay ${selectedPlanState.price} with Paystack
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Cryptocurrency Payment Methods */}
        <div className="grid md:grid-cols-2 gap-4">
          <Card className="cursor-pointer hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Bitcoin className="h-6 w-6 text-primary" />
                  <div>
                    <CardTitle className="text-base">Bitcoin</CardTitle>
                    <CardDescription className="text-sm">Pay with Bitcoin (BTC)</CardDescription>
                  </div>
                </div>
                <Badge variant="secondary" className="bg-orange-100 text-orange-800">Crypto</Badge>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <Button
                onClick={() => handleCryptoPayment('bitcoin')}
                disabled={loading || paymentStep === 'processing'}
                className="w-full"
                variant="outline"
              >
                {activeProvider === 'bitcoin' && paymentStep === 'processing' ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Bitcoin className="h-4 w-4 mr-2" />
                )}
                Pay ${selectedPlanState.price} with Bitcoin
              </Button>
            </CardContent>
          </Card>

          <Card className="cursor-pointer hover:shadow-lg transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Wallet className="h-6 w-6 text-primary" />
                  <div>
                    <CardTitle className="text-base">Ethereum</CardTitle>
                    <CardDescription className="text-sm">Pay with Ethereum (ETH)</CardDescription>
                  </div>
                </div>
                <Badge variant="secondary" className="bg-blue-100 text-blue-800">Crypto</Badge>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <Button
                onClick={() => handleCryptoPayment('ethereum')}
                disabled={loading || paymentStep === 'processing'}
                className="w-full"
                variant="outline"
              >
                {activeProvider === 'ethereum' && paymentStep === 'processing' ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Wallet className="h-4 w-4 mr-2" />
                )}
                Pay ${selectedPlanState.price} with Ethereum
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Security Notice */}
      <Card className="bg-muted/50">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
            <div className="space-y-1">
              <p className="font-medium">Secure Payment Processing</p>
              <p className="text-sm text-muted-foreground">
                All payments are processed securely. Your subscription will be activated immediately 
                after successful payment verification.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EnhancedCryptoPayments;