import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Trophy, Globe, Star } from "lucide-react";
import { sportTypes } from "./SportTypeSelector";

export type League = {
  id: string;
  name: string;
  sport: string;
  country: string;
  tier: 'premium' | 'major' | 'minor';
  icon?: React.ReactNode;
};

export const leagues: League[] = [
  // Football
  { id: 'epl', name: 'Premier League', sport: 'football', country: 'England', tier: 'premium' },
  { id: 'laliga', name: 'La Liga', sport: 'football', country: 'Spain', tier: 'premium' },
  { id: 'seriea', name: 'Serie A', sport: 'football', country: 'Italy', tier: 'premium' },
  { id: 'bundesliga', name: 'Bundesliga', sport: 'football', country: 'Germany', tier: 'premium' },
  { id: 'ligue1', name: 'Ligue 1', sport: 'football', country: 'France', tier: 'major' },
  { id: 'champions', name: 'Champions League', sport: 'football', country: 'Europe', tier: 'premium' },
  
  // Basketball
  { id: 'nba', name: 'NBA', sport: 'basketball', country: 'USA', tier: 'premium' },
  { id: 'euroleague', name: 'EuroLeague', sport: 'basketball', country: 'Europe', tier: 'major' },
  { id: 'ncaa', name: 'NCAA', sport: 'basketball', country: 'USA', tier: 'major' },
  { id: 'gleague', name: 'G League', sport: 'basketball', country: 'USA', tier: 'minor' },
  
  // Baseball
  { id: 'mlb', name: 'MLB', sport: 'baseball', country: 'USA', tier: 'premium' },
  { id: 'npb', name: 'NPB', sport: 'baseball', country: 'Japan', tier: 'major' },
  { id: 'kbo', name: 'KBO', sport: 'baseball', country: 'South Korea', tier: 'major' },
  
  // Tennis
  { id: 'atp', name: 'ATP Tour', sport: 'tennis', country: 'International', tier: 'premium' },
  { id: 'wta', name: 'WTA Tour', sport: 'tennis', country: 'International', tier: 'premium' },
  { id: 'grandslam', name: 'Grand Slam', sport: 'tennis', country: 'International', tier: 'premium' },
  
  // Esports
  { id: 'lol', name: 'League of Legends', sport: 'esports', country: 'Global', tier: 'premium' },
  { id: 'cs2', name: 'Counter-Strike 2', sport: 'esports', country: 'Global', tier: 'premium' },
  { id: 'dota2', name: 'Dota 2', sport: 'esports', country: 'Global', tier: 'major' },
  { id: 'valorant', name: 'Valorant', sport: 'esports', country: 'Global', tier: 'major' },
];

interface LeagueSelectorProps {
  selectedSport?: string;
  selectedLeague?: string;
  onLeagueChange: (league: string) => void;
  showBadges?: boolean;
}

const getTierIcon = (tier: string) => {
  switch (tier) {
    case 'premium':
      return <Star className="h-3 w-3 text-warning" />;
    case 'major':
      return <Trophy className="h-3 w-3 text-info" />;
    default:
      return <Globe className="h-3 w-3 text-muted-foreground" />;
  }
};

export const LeagueSelector = ({ selectedSport, selectedLeague, onLeagueChange, showBadges = false }: LeagueSelectorProps) => {
  const filteredLeagues = selectedSport 
    ? leagues.filter(league => league.sport === selectedSport)
    : leagues;

  if (showBadges) {
    return (
      <div className="flex flex-wrap gap-2">
        <Badge 
          variant={!selectedLeague || selectedLeague === 'all' ? "default" : "secondary"}
          className="cursor-pointer"
          onClick={() => onLeagueChange('all')}
        >
          All Leagues
        </Badge>
        {filteredLeagues.map((league) => (
          <Badge
            key={league.id}
            variant={selectedLeague === league.id ? "default" : "secondary"}
            className="cursor-pointer flex items-center gap-1"
            onClick={() => onLeagueChange(league.id)}
          >
            {getTierIcon(league.tier)}
            {league.name}
          </Badge>
        ))}
      </div>
    );
  }

  return (
    <Select value={selectedLeague || 'all'} onValueChange={onLeagueChange}>
      <SelectTrigger className="w-full">
        <SelectValue placeholder="Select league" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Leagues</SelectItem>
        {filteredLeagues.map((league) => (
          <SelectItem key={league.id} value={league.id}>
            <div className="flex items-center gap-2">
              {getTierIcon(league.tier)}
              <span>{league.name}</span>
              <span className="text-xs text-muted-foreground">({league.country})</span>
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};