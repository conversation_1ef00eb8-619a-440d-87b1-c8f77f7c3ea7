import React, { useState } from 'react';
import { Header } from '@/components/Header';
import MinimalFooter from '@/components/MinimalFooter';
import { SEOHead } from '@/components/SEOHead';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { 
  Mail, 
  Phone, 
  MapPin, 
  Clock, 
  MessageSquare, 
  Send,
  Twitter,
  Facebook,
  Instagram
} from 'lucide-react';

const Contact: React.FC = () => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    category: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const contactInfo = [
    {
      icon: <Mail className="w-6 h-6" />,
      title: "Email Us",
      content: "<EMAIL>",
      description: "We respond within 24 hours"
    },
    {
      icon: <Phone className="w-6 h-6" />,
      title: "Call Us",
      content: "+****************",
      description: "Mon-Fri, 9AM-6PM PST"
    },
    {
      icon: <MapPin className="w-6 h-6" />,
      title: "Visit Us",
      content: "Los Angeles, CA",
      description: "United States"
    },
    {
      icon: <Clock className="w-6 h-6" />,
      title: "Business Hours",
      content: "24/7 Support",
      description: "Always here to help"
    }
  ];

  const socialLinks = [
    { icon: <Twitter className="w-5 h-5" />, label: "Twitter", url: "#" },
    { icon: <Facebook className="w-5 h-5" />, label: "Facebook", url: "#" },
    { icon: <Instagram className="w-5 h-5" />, label: "Instagram", url: "#" }
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: "Message Sent Successfully!",
        description: "We'll get back to you within 24 hours.",
      });

      // Reset form
      setFormData({
        name: '',
        email: '',
        subject: '',
        category: '',
        message: ''
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <>
      <SEOHead
        title="Contact Us - Get Support | 1300BLK AI Sports Engine"
        description="Contact 1300BLK AI Sports Engine for support, questions, or feedback. Get in touch with our expert team for assistance with predictions and platform features."
        keywords="contact 1300BLK AI, sports prediction support, AI betting help, customer service, technical support"
      />

      <div className="min-h-screen bg-gradient-hero">
        <Header />
        
        <main className="container mx-auto px-4 py-16">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-orbitron font-black mb-6">
              <span className="bg-gradient-primary bg-clip-text text-transparent">
                Get In Touch
              </span>
              <br />
              <span className="text-foreground">With Our Team</span>
            </h1>
            <p className="text-xl text-muted-foreground font-exo max-w-3xl mx-auto">
              Have questions about our AI sports predictions? Need technical support? 
              We're here to help you succeed.
            </p>
          </div>

          <div className="grid lg:grid-cols-3 gap-12 max-w-7xl mx-auto">
            {/* Contact Information */}
            <div className="lg:col-span-1 space-y-8">
              <Card className="bg-card/80 backdrop-blur-sm border-border/50 p-6">
                <h2 className="text-2xl font-orbitron font-bold mb-6 text-foreground">
                  Contact Information
                </h2>
                
                <div className="space-y-6">
                  {contactInfo.map((info, index) => (
                    <div key={index} className="flex items-start gap-4">
                      <div className="p-2 bg-primary/10 rounded-lg text-primary flex-shrink-0">
                        {info.icon}
                      </div>
                      <div>
                        <h3 className="font-rajdhani font-semibold text-foreground mb-1">
                          {info.title}
                        </h3>
                        <p className="font-exo text-foreground mb-1">{info.content}</p>
                        <p className="text-sm text-muted-foreground">{info.description}</p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="border-t border-border/50 pt-6 mt-8">
                  <h3 className="font-rajdhani font-semibold text-foreground mb-4">
                    Follow Us
                  </h3>
                  <div className="flex gap-3">
                    {socialLinks.map((social, index) => (
                      <a
                        key={index}
                        href={social.url}
                        className="p-2 bg-primary/10 rounded-lg text-primary hover:bg-primary/20 transition-colors"
                        aria-label={social.label}
                      >
                        {social.icon}
                      </a>
                    ))}
                  </div>
                </div>
              </Card>

              <Card className="bg-gradient-primary/10 border-primary/20 backdrop-blur-sm p-6">
                <div className="flex items-center gap-3 mb-4">
                  <MessageSquare className="w-6 h-6 text-primary" />
                  <h3 className="font-rajdhani font-bold text-foreground">
                    Quick Response
                  </h3>
                </div>
                <p className="text-sm text-muted-foreground font-exo">
                  Our support team typically responds to inquiries within 2-4 hours 
                  during business hours, and within 24 hours on weekends.
                </p>
              </Card>
            </div>

            {/* Contact Form */}
            <div className="lg:col-span-2">
              <Card className="bg-card/80 backdrop-blur-sm border-border/50 p-8">
                <h2 className="text-3xl font-orbitron font-bold mb-8 text-foreground">
                  Send Us a Message
                </h2>

                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="name" className="font-rajdhani font-semibold">
                        Full Name *
                      </Label>
                      <Input
                        id="name"
                        type="text"
                        value={formData.name}
                        onChange={(e) => handleInputChange('name', e.target.value)}
                        className="mt-2 bg-background/50 border-border/50 font-exo"
                        required
                        placeholder="Enter your full name"
                      />
                    </div>

                    <div>
                      <Label htmlFor="email" className="font-rajdhani font-semibold">
                        Email Address *
                      </Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        className="mt-2 bg-background/50 border-border/50 font-exo"
                        required
                        placeholder="Enter your email"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="category" className="font-rajdhani font-semibold">
                        Category *
                      </Label>
                      <Select onValueChange={(value) => handleInputChange('category', value)}>
                        <SelectTrigger className="mt-2 bg-background/50 border-border/50 font-exo">
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="general">General Inquiry</SelectItem>
                          <SelectItem value="technical">Technical Support</SelectItem>
                          <SelectItem value="billing">Billing & Subscription</SelectItem>
                          <SelectItem value="predictions">Predictions & Analysis</SelectItem>
                          <SelectItem value="partnership">Partnership</SelectItem>
                          <SelectItem value="feedback">Feedback</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="subject" className="font-rajdhani font-semibold">
                        Subject *
                      </Label>
                      <Input
                        id="subject"
                        type="text"
                        value={formData.subject}
                        onChange={(e) => handleInputChange('subject', e.target.value)}
                        className="mt-2 bg-background/50 border-border/50 font-exo"
                        required
                        placeholder="Brief subject line"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="message" className="font-rajdhani font-semibold">
                      Message *
                    </Label>
                    <Textarea
                      id="message"
                      value={formData.message}
                      onChange={(e) => handleInputChange('message', e.target.value)}
                      className="mt-2 bg-background/50 border-border/50 font-exo min-h-[150px]"
                      required
                      placeholder="Please describe your inquiry in detail..."
                    />
                  </div>

                  <Button
                    type="submit"
                    size="lg"
                    disabled={isSubmitting}
                    className="w-full bg-gradient-primary hover:opacity-90 text-white font-rajdhani font-semibold"
                  >
                    {isSubmitting ? (
                      <>Sending Message...</>
                    ) : (
                      <>
                        Send Message <Send className="ml-2 w-4 h-4" />
                      </>
                    )}
                  </Button>

                  <p className="text-sm text-muted-foreground font-exo text-center">
                    By submitting this form, you agree to our privacy policy and terms of service.
                  </p>
                </form>
              </Card>
            </div>
          </div>

          {/* FAQ CTA */}
          <Card className="bg-gradient-primary/10 border-primary/20 backdrop-blur-sm p-8 mt-16 text-center">
            <h3 className="text-2xl font-orbitron font-bold mb-4 text-foreground">
              Looking for Quick Answers?
            </h3>
            <p className="text-muted-foreground font-exo mb-6 max-w-2xl mx-auto">
              Check out our comprehensive FAQ section where we've answered the most 
              common questions about our platform and services.
            </p>
            <Button 
              variant="outline" 
              className="font-rajdhani font-semibold border-primary/20 hover:bg-primary/10"
              asChild
            >
              <a href="/faq">
                Visit FAQ Section
              </a>
            </Button>
          </Card>
        </main>

        <MinimalFooter />
      </div>
    </>
  );
};

export default Contact;