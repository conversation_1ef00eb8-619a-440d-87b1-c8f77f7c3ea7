import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Lock, TrendingUp, Clock } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { apiService } from '@/services/api';
import { format } from 'date-fns';

interface SimplePrediction {
  id: string;
  sport: string;
  league: string;
  home_team: string;
  away_team: string;
  match_date: string;
  market: string;
  pick: string;
  confidence: number;
  odds: number;
  edge?: number | null;
  source: string;
  status: string;
  is_premium?: boolean | null;
}

const SimplePredictionsTable: React.FC = () => {
  const [predictions, setPredictions] = useState<SimplePrediction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { profile } = useAuth();
  
  // User separation logic - show based on subscription and user role
  const canViewPremium = profile?.subscription_status === 'active' || 
                        profile?.role === 'admin' || 
                        profile?.role === 'super_admin';

  useEffect(() => {
    const fetchPredictions = async () => {
      try {
        const response = await apiService.getAllPredictions();

        if (!response.success) throw new Error(response.error);
        
        // Remove duplicates with enhanced logic including match_date
        const uniquePredictions = (response.data?.predictions || []).filter((prediction, index, self) => {
          const key = `${prediction.sport}-${prediction.league}-${prediction.home_team}-${prediction.away_team}-${prediction.market}-${prediction.match_date}`;
          return index === self.findIndex(p => {
            const compareKey = `${p.sport}-${p.league}-${p.home_team}-${p.away_team}-${p.market}-${p.match_date}`;
            return compareKey === key;
          });
        });
        
        setPredictions(uniquePredictions);
      } catch (err) {
        console.error('Error fetching predictions:', err);
        setError('Failed to load predictions');
      } finally {
        setLoading(false);
      }
    };

    fetchPredictions();
  }, []);

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-success';
    if (confidence >= 60) return 'text-warning';
    return 'text-muted-foreground';
  };

  const getEdgeColor = (edge: number) => {
    if (edge >= 10) return 'text-success';
    if (edge >= 5) return 'text-warning';
    return 'text-muted-foreground';
  };

  if (loading) {
    return (
      <Card className="bg-gradient-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-bruno">
            <TrendingUp className="h-5 w-5" />
            Live Predictions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-16 bg-muted/20 rounded-md"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="bg-gradient-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-bruno">
            <TrendingUp className="h-5 w-5" />
            Live Predictions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-destructive text-center py-8">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (predictions.length === 0) {
    return (
      <Card className="bg-gradient-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-bruno">
            <TrendingUp className="h-5 w-5" />
            Live Predictions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-8 font-bruno">No live predictions available at the moment.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="neumorphic shadow-glow">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 font-bruno drop-shadow-md">
          <TrendingUp className="h-5 w-5" />
          Live Predictions
          <Badge variant="secondary" className="ml-auto">
            {predictions.length} Active
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {predictions.map((prediction) => {
            const isPremium = prediction.is_premium;
            const canView = canViewPremium || !isPremium;

            return (
              <div
                key={prediction.id}
                className={`neumorphic micro-bounce micro-glow transition-all duration-300 ${
                  isPremium ? 'border-warning/30' : ''
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs font-bruno">
                      {prediction.sport}
                    </Badge>
                    <Badge variant="secondary" className="text-xs font-bruno">
                      {prediction.league}
                    </Badge>
                    {isPremium && (
                      <Badge className="bg-gradient-primary text-xs font-bruno drop-shadow-sm">
                        Premium
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground font-bruno">
                    <Clock className="h-3 w-3" />
                    {format(new Date(prediction.match_date), 'MMM dd, HH:mm')}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold text-sm mb-1 font-bruno drop-shadow-sm">
                      {prediction.home_team} vs {prediction.away_team}
                    </h4>
                    <p className="text-xs text-muted-foreground font-bruno">
                      {prediction.market}
                    </p>
                  </div>

                  {canView ? (
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium font-bruno">Pick:</span>
                        <Badge variant="default" className="font-bruno">{prediction.pick}</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-bruno">Confidence:</span>
                        <span className={`text-sm font-bold font-bruno drop-shadow-sm ${getConfidenceColor(prediction.confidence)}`}>
                          {prediction.confidence}%
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-bruno">Edge:</span>
                        <span className={`text-sm font-bold font-bruno drop-shadow-sm ${getEdgeColor(prediction.edge || 0)}`}>
                          {(prediction.edge || 0) > 0 ? '+' : ''}{(prediction.edge || 0).toFixed(1)}%
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-bruno">Odds:</span>
                        <span className="text-sm font-medium font-bruno">
                          {prediction.odds > 0 ? '+' : ''}{prediction.odds}
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center p-4 bg-muted/20 rounded-md">
                      <div className="text-center">
                        <Lock className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
                        <p className="text-sm text-muted-foreground mb-2 font-bruno">Premium Prediction</p>
                        <Button size="sm" variant="outline" className="font-bruno">
                          Upgrade to View
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default SimplePredictionsTable;