import React from 'react';
import { AdSenseInline } from './AdSense';

const PartnersSection: React.FC = () => {
  const partners = [
    { name: "Lovable", url: "https://lovable.dev" },
    { name: "OpenAI", url: "https://openai.com" },
    { name: "Python", url: "https://python.org" },
    { name: "Node.js", url: "https://nodejs.org" },
    { name: "TypeScript", url: "https://typescriptlang.org" },
    { name: "Lumora Tech", url: "#" },
    { name: "Google", url: "https://google.com" },
    { name: "Google AI Studio", url: "https://aistudio.google.com" },
    { name: "<PERSON>", url: "https://gemini.google.com" }
  ];

  return (
    <>
      <section className="py-8 bg-background border-t border-border/20 overflow-hidden">
        <div className="container mx-auto px-4">
          <div className="text-center mb-4">
            <h3 className="text-sm font-bruno tracking-wide text-muted-foreground/70 mb-4">
              POWERED BY
            </h3>
          </div>

          {/* Sliding Partners */}
          <div className="relative">
            <div className="flex animate-[slide_20s_linear_infinite] whitespace-nowrap">
              {[...partners, ...partners].map((partner, index) => (
                <a
                  key={index}
                  href={partner.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center mx-4 sm:mx-6 lg:mx-8 text-red-500 hover:text-red-400 transition-colors duration-300 text-xs sm:text-sm font-medium uppercase"
                >
                  {partner.name}
                </a>
              ))}
            </div>
          </div>
        </div>
      </section>
      
      {/* Google Ads Card */}
      <div className="container mx-auto px-4 py-4">
        <AdSenseInline className="max-w-md mx-auto" />
      </div>
    </>
  );
};

export default PartnersSection;