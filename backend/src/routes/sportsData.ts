import { Router } from 'express';
import {
  getAllSportsData,
  getSportsDataById,
  createSportsData,
  updateSportsData,
  deleteSportsData,
  bulkCreateSportsData,
  getSportsDataStats,
} from '@/controllers/sportsDataController';
import { authenticate, authorize, optionalAuth } from '@/middleware/auth';
// Removed enum imports - using string literals instead
import { validate, validateQuery } from '@/utils/validation';
import { sportsDataSchema, paginationSchema, sportsFilterSchema, dateRangeSchema } from '@/utils/validation';

const router = Router();

// Public routes (with optional auth for premium content)
router.get('/', optionalAuth, validateQuery(paginationSchema.concat(sportsFilterSchema).concat(dateRangeSchema)), getAllSportsData);
router.get('/stats', optionalAuth, getSportsDataStats);
router.get('/:id', optionalAuth, getSportsDataById);

// Protected routes - Admin only
router.post('/', authenticate, authorize('ADMIN', 'SUPER_ADMIN'), validate(sportsDataSchema), createSportsData);
router.put('/:id', authenticate, authorize('ADMIN', 'SUPER_ADMIN'), updateSportsData);
router.delete('/:id', authenticate, authorize('ADMIN', 'SUPER_ADMIN'), deleteSportsData);
router.post('/bulk', authenticate, authorize('ADMIN', 'SUPER_ADMIN'), bulkCreateSportsData);

export default router;
