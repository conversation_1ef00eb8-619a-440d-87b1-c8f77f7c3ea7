import { Request, Response, NextFunction } from 'express';
import { prisma } from '@/config/database';
import { createError } from '@/middleware/errorHandler';
import { AuthenticatedRequest } from '@/middleware/auth';
// Removed enum imports - using string literals instead

export const getAllSportsData = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      sport, 
      league, 
      status, 
      startDate, 
      endDate,
      homeTeam,
      awayTeam 
    } = req.query;
    
    const skip = (Number(page) - 1) * Number(limit);
    
    const where: any = {};
    if (sport) where.sport = sport;
    if (league) where.league = league;
    if (status) where.status = status;
    if (homeTeam) where.homeTeam = { contains: homeTeam as string, mode: 'insensitive' };
    if (awayTeam) where.awayTeam = { contains: awayTeam as string, mode: 'insensitive' };
    
    if (startDate || endDate) {
      where.matchDate = {};
      if (startDate) where.matchDate.gte = new Date(startDate as string);
      if (endDate) where.matchDate.lte = new Date(endDate as string);
    }

    const [matches, total] = await Promise.all([
      prisma.sportsData.findMany({
        where,
        skip,
        take: Number(limit),
        orderBy: { matchDate: 'asc' },
        include: {
          predictions: {
            select: {
              id: true,
              market: true,
              pick: true,
              confidence: true,
              isPremium: true,
            },
          },
        },
      }),
      prisma.sportsData.count({ where }),
    ]);

    res.json({
      success: true,
      data: {
        matches,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit)),
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getSportsDataById = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const match = await prisma.sportsData.findUnique({
      where: { id },
      include: {
        predictions: {
          orderBy: { createdAt: 'desc' },
        },
      },
    });

    if (!match) {
      return next(createError('Match not found', 404));
    }

    res.json({
      success: true,
      data: { match },
    });
  } catch (error) {
    next(error);
  }
};

export const createSportsData = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const matchData = req.body;

    // Check for duplicate match
    if (matchData.externalId && matchData.source) {
      const existingMatch = await prisma.sportsData.findFirst({
        where: {
          externalId: matchData.externalId,
          source: matchData.source,
        },
      });

      if (existingMatch) {
        return next(createError('Match already exists for this source', 409));
      }
    }

    const match = await prisma.sportsData.create({
      data: {
        ...matchData,
        matchDate: new Date(matchData.matchDate),
      },
    });

    res.status(201).json({
      success: true,
      message: 'Sports data created successfully',
      data: { match },
    });
  } catch (error) {
    next(error);
  }
};

export const updateSportsData = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const existingMatch = await prisma.sportsData.findUnique({
      where: { id },
    });

    if (!existingMatch) {
      return next(createError('Match not found', 404));
    }

    const updatedMatch = await prisma.sportsData.update({
      where: { id },
      data: {
        ...updateData,
        ...(updateData.matchDate && { matchDate: new Date(updateData.matchDate) }),
      },
    });

    res.json({
      success: true,
      message: 'Sports data updated successfully',
      data: { match: updatedMatch },
    });
  } catch (error) {
    next(error);
  }
};

export const deleteSportsData = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { id } = req.params;

    const existingMatch = await prisma.sportsData.findUnique({
      where: { id },
    });

    if (!existingMatch) {
      return next(createError('Match not found', 404));
    }

    await prisma.sportsData.delete({
      where: { id },
    });

    res.json({
      success: true,
      message: 'Sports data deleted successfully',
    });
  } catch (error) {
    next(error);
  }
};

export const bulkCreateSportsData = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const { matches } = req.body;

    if (!Array.isArray(matches) || matches.length === 0) {
      return next(createError('Matches array is required', 400));
    }

    const results = [];
    const errors = [];

    for (const matchData of matches) {
      try {
        // Check for duplicate
        if (matchData.externalId && matchData.source) {
          const existing = await prisma.sportsData.findFirst({
            where: {
              externalId: matchData.externalId,
              source: matchData.source,
            },
          });

          if (existing) {
            // Update existing match
            const updated = await prisma.sportsData.update({
              where: { id: existing.id },
              data: {
                ...matchData,
                matchDate: new Date(matchData.matchDate),
              },
            });
            results.push({ action: 'updated', match: updated });
          } else {
            // Create new match
            const created = await prisma.sportsData.create({
              data: {
                ...matchData,
                matchDate: new Date(matchData.matchDate),
              },
            });
            results.push({ action: 'created', match: created });
          }
        } else {
          // Create without duplicate check
          const created = await prisma.sportsData.create({
            data: {
              ...matchData,
              matchDate: new Date(matchData.matchDate),
            },
          });
          results.push({ action: 'created', match: created });
        }
      } catch (error) {
        errors.push({
          matchData,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    res.json({
      success: true,
      message: `Processed ${matches.length} matches`,
      data: {
        results,
        errors,
        summary: {
          total: matches.length,
          successful: results.length,
          failed: errors.length,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};

export const getSportsDataStats = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const [
      totalMatches,
      upcomingMatches,
      liveMatches,
      completedMatches,
      sportBreakdown,
      recentMatches,
    ] = await Promise.all([
      prisma.sportsData.count(),
      prisma.sportsData.count({ where: { status: 'UPCOMING' } }),
      prisma.sportsData.count({ where: { status: 'LIVE' } }),
      prisma.sportsData.count({ where: { status: 'COMPLETED' } }),
      prisma.sportsData.groupBy({
        by: ['sport'],
        _count: { sport: true },
        orderBy: { _count: { sport: 'desc' } },
      }),
      prisma.sportsData.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          },
        },
      }),
    ]);

    res.json({
      success: true,
      data: {
        totalMatches,
        upcomingMatches,
        liveMatches,
        completedMatches,
        recentMatches,
        sportBreakdown: sportBreakdown.map(item => ({
          sport: item.sport,
          count: item._count.sport,
        })),
      },
    });
  } catch (error) {
    next(error);
  }
};
