import { Router } from 'express';
import { authenticate, authorize } from '@/middleware/auth';
import {
  getAutomationStatus,
  startAutomation,
  stopAutomation,
  executePipeline,
  updateAutomationConfig,
  getAutomationMetrics,
  testAutomation,
  getAutomationLogs,
} from '@/controllers/automationController';

const router = Router();

// All automation routes require admin access
router.use(authenticate, authorize('admin'));

/**
 * @route GET /api/automation/status
 * @desc Get automation system status
 * @access Admin
 */
router.get('/status', getAutomationStatus);

/**
 * @route POST /api/automation/start
 * @desc Start automated scraping and prediction system
 * @access Admin
 * @body {
 *   sports?: string[],
 *   leagues?: string[],
 *   interval?: string,
 *   providers?: string[],
 *   autoPredictions?: boolean
 * }
 */
router.post('/start', startAutomation);

/**
 * @route POST /api/automation/stop
 * @desc Stop automated scraping and prediction system
 * @access Admin
 */
router.post('/stop', stopAutomation);

/**
 * @route POST /api/automation/execute-pipeline
 * @desc Execute data pipeline manually
 * @access Admin
 * @body {
 *   sport: string,
 *   league?: string
 * }
 */
router.post('/execute-pipeline', executePipeline);

/**
 * @route PUT /api/automation/config
 * @desc Update automation configuration
 * @access Admin
 * @body {
 *   enabled?: boolean,
 *   sports?: string[],
 *   leagues?: string[],
 *   interval?: string,
 *   providers?: string[],
 *   autoPredictions?: boolean,
 *   maxConcurrent?: number
 * }
 */
router.put('/config', updateAutomationConfig);

/**
 * @route GET /api/automation/metrics
 * @desc Get automation metrics and statistics
 * @access Admin
 * @query {
 *   timeframe?: '1h' | '24h' | '7d' | '30d'
 * }
 */
router.get('/metrics', getAutomationMetrics);

/**
 * @route POST /api/automation/test
 * @desc Test automation system components
 * @access Admin
 * @body {
 *   component?: 'all' | 'scheduler' | 'pipeline' | 'scraping'
 * }
 */
router.post('/test', testAutomation);

/**
 * @route GET /api/automation/logs
 * @desc Get automation system logs
 * @access Admin
 * @query {
 *   limit?: number,
 *   offset?: number,
 *   level?: 'all' | 'error' | 'warn' | 'info'
 * }
 */
router.get('/logs', getAutomationLogs);

export default router;
