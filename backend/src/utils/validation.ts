import Joi from 'joi';

// User validation schemas
export const registerSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().min(6).required(),
  fullName: Joi.string().min(2).max(100).optional(),
});

export const loginSchema = Joi.object({
  email: Joi.string().email().required(),
  password: Joi.string().required(),
});

export const updateUserSchema = Joi.object({
  fullName: Joi.string().min(2).max(100).optional(),
  email: Joi.string().email().optional(),
});

export const changePasswordSchema = Joi.object({
  currentPassword: Joi.string().required(),
  newPassword: Joi.string().min(6).required(),
});

// Sports data validation schemas
export const sportsDataSchema = Joi.object({
  matchId: Joi.string().optional(),
  sport: Joi.string().required(),
  league: Joi.string().required(),
  homeTeam: Joi.string().required(),
  awayTeam: Joi.string().required(),
  matchDate: Joi.date().required(),
  odds: Joi.object().optional(),
  stats: Joi.object().optional(),
  source: Joi.string().required(),
});

// Prediction validation schemas
export const predictionSchema = Joi.object({
  sport: Joi.string().required(),
  league: Joi.string().required(),
  homeTeam: Joi.string().required(),
  awayTeam: Joi.string().required(),
  matchDate: Joi.date().required(),
  market: Joi.string().required(),
  pick: Joi.string().required(),
  confidence: Joi.number().min(0).max(100).required(),
  odds: Joi.number().required(),
  edge: Joi.number().optional(),
  expectedValue: Joi.number().optional(),
  factors: Joi.object().optional(),
  analysis: Joi.string().optional(),
  isPremium: Joi.boolean().optional(),
});

// Scraping validation schemas
export const scrapingJobSchema = Joi.object({
  provider: Joi.string().valid('espn', 'odds-api', 'sports-reference', 'sports-radar').required(),
  sport: Joi.string().required(),
  league: Joi.string().optional(),
});

// Query parameter validation
export const paginationSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
});

export const dateRangeSchema = Joi.object({
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional(),
});

export const sportsFilterSchema = Joi.object({
  sport: Joi.string().optional(),
  league: Joi.string().optional(),
  status: Joi.string().valid('UPCOMING', 'LIVE', 'COMPLETED', 'POSTPONED', 'CANCELED').optional(),
});

export const predictionFilterSchema = Joi.object({
  sport: Joi.string().optional(),
  league: Joi.string().optional(),
  status: Joi.string().valid('UPCOMING', 'LIVE', 'COMPLETED', 'VOID').optional(),
  isPremium: Joi.boolean().optional(),
  minConfidence: Joi.number().min(0).max(100).optional(),
});

// Validation middleware
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.body);
    
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'Validation error',
        details: error.details.map(detail => detail.message),
      });
    }
    
    req.body = value;
    next();
  };
};

export const validateQuery = (schema: Joi.ObjectSchema) => {
  return (req: any, res: any, next: any) => {
    const { error, value } = schema.validate(req.query);
    
    if (error) {
      return res.status(400).json({
        success: false,
        error: 'Query validation error',
        details: error.details.map(detail => detail.message),
      });
    }
    
    req.query = value;
    next();
  };
};
