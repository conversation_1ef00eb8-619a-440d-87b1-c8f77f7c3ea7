@tailwind base;
@tailwind components;
@tailwind utilities;

/* Sports Predictions Design System with Bruno Ace SC Typography */

@layer base {
  :root {
    /* Pure Black Theme with Red Accents & Enhanced Typography */
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;

    --card: 0 0% 0%;
    --card-foreground: 0 0% 100%;

    --popover: 0 0% 0%;
    --popover-foreground: 0 0% 100%;

    --primary: 0 100% 50%;
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 0%;
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 0%;
    --muted-foreground: 0 0% 70%;

    --accent: 0 100% 50%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;

    --border: 0 0% 100%;
    --input: 0 0% 0%;
    --ring: 0 100% 50%;

    /* Sports-Specific Colors */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    
    --warning: 47 96% 53%;
    --warning-foreground: 0 0% 98%;
    
    --info: 199 89% 48%;
    --info-foreground: 0 0% 98%;

    /* Confidence & Prediction Colors */
    --confidence-high: 142 76% 36%;
    --confidence-medium: 47 96% 53%; 
    --confidence-low: 0 62.8% 30.6%;

    /* Sports Brand Colors */
    --football: 199 89% 48%;
    --basketball: 24 95% 53%;
    --tennis: 142 76% 36%;
    --baseball: 0 62.8% 30.6%;

    /* Red Accent Gradients on Pure Black */
    --gradient-primary: linear-gradient(135deg, hsl(0 100% 50%), hsl(0 100% 40%));
    --gradient-success: linear-gradient(135deg, hsl(0 100% 50%), hsl(0 100% 60%));
    --gradient-hero: linear-gradient(135deg, hsl(0 0% 0%) 0%, hsl(0 0% 0%) 100%);
    --gradient-card: linear-gradient(145deg, hsl(0 0% 2%) 0%, hsl(0 0% 1%) 100%);
    --gradient-neumorphic: linear-gradient(145deg, hsl(0 0% 8%) 0%, hsl(0 0% 4%) 100%);

    /* Enhanced Shadows & Typography Effects */
    --shadow-lg: 0 10px 15px -3px hsl(0 0% 0% / 0.8), 0 4px 6px -2px hsl(0 0% 0% / 0.6);
    --shadow-glow: 0 0 20px hsl(0 100% 50% / 0.3);
    --shadow-card: 0 4px 6px -1px hsl(0 0% 0% / 0.6);
    --shadow-carved: inset 0 2px 4px hsl(0 0% 0% / 0.9), inset 0 -2px 4px hsl(0 0% 0% / 0.9);
    --shadow-carved-deep: inset 0 4px 8px hsl(0 0% 0% / 1), inset 0 -4px 8px hsl(0 0% 0% / 1), 0 0 0 1px hsl(0 0% 100% / 0.2);
    --shadow-neumorphic: 8px 8px 16px hsl(0 0% 0% / 0.8), -8px -8px 16px hsl(0 0% 12% / 0.2);
    --shadow-neumorphic-inset: inset 8px 8px 16px hsl(0 0% 0% / 0.8), inset -8px -8px 16px hsl(0 0% 12% / 0.2);
    --text-shadow-glow: 0 0 10px hsl(0 100% 50% / 0.6), 0 0 20px hsl(0 100% 50% / 0.4);
    --text-shadow-primary: 2px 2px 4px hsl(0 0% 0% / 0.8);
    --text-shadow-hero: 4px 4px 8px hsl(0 0% 0% / 1), 0 0 20px hsl(0 100% 50% / 0.3);

    /* Animation Values */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease-in-out;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .light {
    /* Light Theme with Red Accents & Enhanced Typography */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;

    --primary: 0 100% 50%;
    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;

    --accent: 0 100% 50%;
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 100% 50%;

    /* Sports-Specific Colors - Light Theme */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;

    --warning: 47 96% 53%;
    --warning-foreground: 0 0% 98%;

    --info: 221 83% 53%;
    --info-foreground: 0 0% 98%;

    /* Confidence Level Colors - Light Theme */
    --confidence-high: 142 76% 36%;
    --confidence-medium: 47 96% 53%;
    --confidence-low: 0 84% 60%;

    /* Sport-Specific Colors - Light Theme */
    --football: 142 76% 36%;
    --basketball: 24 95% 53%;
    --tennis: 47 96% 53%;
    --baseball: 221 83% 53%;

    /* Enhanced Gradients - Light Theme */
    --gradient-primary: linear-gradient(135deg, hsl(0 100% 50%) 0%, hsl(0 100% 40%) 100%);
    --gradient-success: linear-gradient(135deg, hsl(142 76% 36%) 0%, hsl(142 76% 26%) 100%);
    --gradient-hero: linear-gradient(135deg, hsl(0 0% 100%) 0%, hsl(0 0% 96%) 50%, hsl(0 0% 100%) 100%);
    --gradient-card: linear-gradient(135deg, hsl(0 0% 100%) 0%, hsl(0 0% 98%) 100%);
    --gradient-neumorphic: linear-gradient(145deg, hsl(0 0% 98%) 0%, hsl(0 0% 100%) 100%);

    /* Enhanced Shadows - Light Theme */
    --shadow-glow: 0 0 20px hsl(0 100% 50% / 0.3);
    --shadow-card: 0 4px 6px -1px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.06);
    --shadow-carved: inset 2px 2px 5px hsl(0 0% 0% / 0.1), inset -2px -2px 5px hsl(0 0% 100% / 0.8);
    --shadow-carved-deep: inset 4px 4px 8px hsl(0 0% 0% / 0.15), inset -4px -4px 8px hsl(0 0% 100% / 0.9);
    --shadow-neumorphic: 8px 8px 16px hsl(0 0% 0% / 0.1), -8px -8px 16px hsl(0 0% 100% / 0.8);

    /* Text Shadows - Light Theme */
    --text-shadow-glow: 0 0 10px hsl(0 100% 50% / 0.5);
    --text-shadow-primary: 1px 1px 2px hsl(0 0% 0% / 0.3);
    --text-shadow-hero: 2px 2px 4px hsl(0 0% 0% / 0.3), 0 0 10px hsl(0 100% 50% / 0.2);

    /* Sidebar - Light Theme */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
  
  /* Bruno Ace SC Typography Enhancements */
  .font-bruno {
    font-family: 'Bruno Ace SC', sans-serif;
    text-shadow: var(--text-shadow-primary);
  }

  /* Apply Bruno font to all headers */
  h1, h2, h3, h4, h5, h6,
  .heading,
  .title {
    font-family: 'Bruno Ace SC', sans-serif !important;
    text-shadow: var(--text-shadow-primary);
  }

  /* Make all button text uppercase */
  button,
  .btn,
  [role="button"],
  .button {
    text-transform: uppercase !important;
    letter-spacing: 0.5px;
    font-weight: 600;
  }
  
  .text-shadow-glow {
    text-shadow: var(--text-shadow-glow);
  }
  
  .text-shadow-hero {
    text-shadow: var(--text-shadow-hero);
  }
  
  /* Enhanced Drop Shadow Utilities */
  .drop-shadow-glow {
    filter: drop-shadow(0 0 10px hsl(0 100% 50% / 0.6));
  }
  
  .drop-shadow-primary {
    filter: drop-shadow(2px 2px 4px hsl(0 0% 0% / 0.8));
  }
  
  /* Neumorphic Design Components */
  .neumorphic {
    background: var(--gradient-neumorphic);
    box-shadow: var(--shadow-neumorphic);
    border: 1px solid hsl(0 0% 10%);
    transition: var(--transition-smooth);
  }
  
  .neumorphic:hover {
    transform: translateY(-2px);
    box-shadow: 12px 12px 24px hsl(0 0% 0% / 0.9), -12px -12px 24px hsl(0 0% 15% / 0.3);
  }
  
  .neumorphic-inset {
    background: var(--gradient-neumorphic);
    box-shadow: var(--shadow-neumorphic-inset);
    border: 1px solid hsl(0 0% 5%);
  }
  
  /* Micro Interactions */
  .micro-bounce {
    transition: var(--transition-fast);
  }
  
  .micro-bounce:hover {
    transform: scale(1.05);
  }
  
  .micro-glow {
    transition: var(--transition-smooth);
  }
  
  .micro-glow:hover {
    box-shadow: 0 0 20px hsl(0 100% 50% / 0.4);
  }
  
  .pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
  
  /* AOS Animation Classes */
  .aos-card {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }
  
  .aos-card.animate {
    opacity: 1;
    transform: translateY(0);
  }
}