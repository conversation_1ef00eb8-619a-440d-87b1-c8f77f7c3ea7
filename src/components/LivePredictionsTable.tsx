
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Lock, TrendingUp, Clock } from 'lucide-react';
import { useLivePredictions } from '@/hooks/useLivePredictions';
import { useSubscription } from '@/hooks/useSubscription';
import { useAuth } from '@/contexts/AuthContext';
import { format } from 'date-fns';

const LivePredictionsTable: React.FC = () => {
  const { predictions, loading, error } = useLivePredictions();
  const { subscribed } = useSubscription();
  const { profile } = useAuth();
  
  // User separation logic - show based on subscription and user role
  const canViewPremium = profile?.subscription_status === 'active' || 
                        profile?.role === 'admin' || 
                        profile?.role === 'super_admin' || 
                        subscribed;

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 80) return 'text-success';
    if (confidence >= 60) return 'text-warning';
    return 'text-muted-foreground';
  };

  const getEdgeColor = (edge: number) => {
    if (edge >= 10) return 'text-success';
    if (edge >= 5) return 'text-warning';
    return 'text-muted-foreground';
  };

  if (loading) {
    return (
      <Card className="bg-gradient-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Live Predictions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-16 bg-muted/20 rounded-md"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="bg-gradient-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Live Predictions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-destructive text-center py-8">{error}</p>
        </CardContent>
      </Card>
    );
  }

  if (predictions.length === 0) {
    return (
      <Card className="bg-gradient-card">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Live Predictions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground text-center py-8">No live predictions available at the moment.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gradient-card">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Live Predictions
          <Badge variant="secondary" className="ml-auto">
            {predictions.length} Active
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {predictions.map((prediction) => {
            const isPremium = prediction.is_premium;
            const canView = canViewPremium || !isPremium;

            return (
              <div
                key={prediction.id}
                className={`p-4 rounded-lg border ${
                  isPremium ? 'border-warning/30 bg-warning/5' : 'border-border bg-background/50'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline" className="text-xs">
                      {prediction.sport}
                    </Badge>
                    <Badge variant="secondary" className="text-xs">
                      {prediction.league}
                    </Badge>
                    {isPremium && (
                      <Badge className="bg-gradient-primary text-xs">
                        Premium
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center gap-2 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    {format(new Date(prediction.match_date), 'MMM dd, HH:mm')}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-semibold text-sm mb-1">
                      {prediction.home_team} vs {prediction.away_team}
                    </h4>
                    <p className="text-xs text-muted-foreground">
                      {prediction.market}
                    </p>
                  </div>

                  {canView ? (
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">Pick:</span>
                        <Badge variant="default">{prediction.pick}</Badge>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Confidence:</span>
                        <span className={`text-sm font-bold ${getConfidenceColor(prediction.confidence)}`}>
                          {prediction.confidence}%
                        </span>
                      </div>
                       <div className="flex justify-between items-center">
                        <span className="text-sm">Edge:</span>
                        <span className={`text-sm font-bold ${getEdgeColor(prediction.edge || 0)}`}>
                          {(prediction.edge || 0) > 0 ? '+' : ''}{(prediction.edge || 0).toFixed(1)}%
                        </span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">Odds:</span>
                        <span className="text-sm font-medium">
                          {prediction.odds > 0 ? '+' : ''}{prediction.odds}
                        </span>
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center justify-center p-4 bg-muted/20 rounded-md">
                      <div className="text-center">
                        <Lock className="h-6 w-6 mx-auto mb-2 text-muted-foreground" />
                        <p className="text-sm text-muted-foreground mb-2">Premium Prediction</p>
                        <Button size="sm" variant="outline">
                          Upgrade to View
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
};

export default LivePredictionsTable;
