import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, Download, Globe, Zap, Database, Clock, Play, Pause } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiService } from '@/services/api';

interface ScrapedData {
  id: string;
  source_url: string;
  scraped_at: string;
  payload: any;
}

interface ProcessedMatch {
  match_id: string;
  home_team: string;
  away_team: string;
  match_date: string;
  league?: string;
  sport?: string;
  stats?: any;
}

export const SportsDataScraper: React.FC = () => {
  const { toast } = useToast();
  const [sourceUrls, setSourceUrls] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const [scrapedData, setScrapedData] = useState<ScrapedData[]>([]);
  const [processedMatches, setProcessedMatches] = useState<ProcessedMatch[]>([]);
  const [isRealTimeActive, setIsRealTimeActive] = useState(false);

  // Real-time subscription for scraped data
  useEffect(() => {
    if (!isRealTimeActive) return;

    // Set up polling for real-time updates (since we don't have WebSocket yet)
    const interval = setInterval(() => {
      loadExistingData();
    }, 30000); // Poll every 30 seconds

    return () => {
      clearInterval(interval);
    };
  }, [isRealTimeActive, toast]);

  // Load existing data
  useEffect(() => {
    loadExistingData();
  }, []);

  const loadExistingData = async () => {
    try {
      // Load scraped data from API
      const rawDataResponse = await apiService.getSportsData();
      if (rawDataResponse.success && rawDataResponse.data) {
        setScrapedData(rawDataResponse.data.slice(0, 20));
      }

      // Load processed matches from API
      const matchDataResponse = await apiService.getAllPredictions();
      if (matchDataResponse.success && matchDataResponse.data?.predictions) {
        setProcessedMatches(matchDataResponse.data.predictions.slice(0, 20));
      }

    } catch (error) {
      console.error('Error loading data:', error);
      toast({
        title: "Error",
        description: "Failed to load existing data",
        variant: "destructive",
      });
    }
  };

  const handleScrapeSubmit = async () => {
    if (!sourceUrls.trim()) {
      toast({
        title: "Error",
        description: "Please enter at least one URL to scrape",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const urls = sourceUrls.split('\n').filter(url => url.trim());
      const scrapePromises = urls.map(url => scrapeUrl(url.trim()));
      
      const results = await Promise.allSettled(scrapePromises);
      
      let successCount = 0;
      let errorCount = 0;

      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          successCount++;
        } else {
          errorCount++;
          console.error(`Error scraping ${urls[index]}:`, result.reason);
        }
      });

      toast({
        title: "Scraping Complete",
        description: `${successCount} successful, ${errorCount} failed`,
        variant: errorCount > 0 ? "destructive" : "default",
      });

      // Clear URLs after successful scrape
      if (successCount > 0) {
        setSourceUrls('');
      }

    } catch (error) {
      console.error('Scraping error:', error);
      toast({
        title: "Error",
        description: "Failed to complete scraping",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const scrapeUrl = async (url: string) => {
    // Use the web-scraper API
    const response = await apiService.scrapeWebData({
      url,
      type: 'html',
      useCache: true,
      timeout: 15000,
      waitFor: 1000
    });

    if (!response.success) throw new Error(response.error);

    // Store scraped data in raw_data table
    const insertResponse = await apiService.saveRawData({
      source_url: url,
      payload: response.data.data || response.data,
      scraped_at: new Date().toISOString()
    });

    if (!insertResponse.success) throw new Error(insertResponse.error);

    return insertResponse.data;
  };

  const handleProcessData = async (rawDataId?: string) => {
    try {
      setIsLoading(true);

      const response = await apiService.processSportsData({
        rawDataId,
        processAll: !rawDataId
      });

      if (!response.success) throw new Error(response.error);

      toast({
        title: "Processing Complete",
        description: `Processed ${response.data.processed} matches`,
      });

    } catch (error) {
      console.error('Processing error:', error);
      toast({
        title: "Error",
        description: "Failed to process data",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGeneratePredictions = async (matchId?: string) => {
    try {
      setIsLoading(true);

      const response = await apiService.generatePrediction({
        matchId,
        generateAll: !matchId
      });

      if (!response.success) throw new Error(response.error);

      toast({
        title: "Predictions Generated",
        description: `Generated ${response.data.predictions} predictions`,
      });

    } catch (error) {
      console.error('Prediction error:', error);
      toast({
        title: "Error",
        description: "Failed to generate predictions",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleRealTime = () => {
    setIsRealTimeActive(!isRealTimeActive);
    toast({
      title: isRealTimeActive ? "Real-time Disabled" : "Real-time Enabled",
      description: isRealTimeActive ? "Stopped listening for updates" : "Now listening for live updates",
    });
  };

  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      <Card className="border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="w-5 h-5 text-primary" />
            Sports Data Pipeline
            <Button
              onClick={toggleRealTime}
              variant={isRealTimeActive ? "destructive" : "outline"}
              size="sm"
              className="ml-auto"
            >
              {isRealTimeActive ? <Pause className="w-4 h-4 mr-2" /> : <Play className="w-4 h-4 mr-2" />}
              {isRealTimeActive ? "Stop" : "Start"} Real-time
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <label className="text-sm font-medium">Source URLs (one per line)</label>
            <textarea
              placeholder="https://example.com/sports-data&#10;https://another-source.com/matches"
              value={sourceUrls}
              onChange={(e) => setSourceUrls(e.target.value)}
              className="w-full min-h-[120px] p-3 border rounded-md font-mono text-sm"
            />
          </div>

          <div className="flex gap-4">
            <Button 
              onClick={handleScrapeSubmit}
              disabled={isLoading || !sourceUrls.trim()}
              size="lg"
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Zap className="w-4 h-4 mr-2 animate-spin" />
                  Scraping...
                </>
              ) : (
                <>
                  <Download className="w-4 h-4 mr-2" />
                  Scrape Data
                </>
              )}
            </Button>

            <Button 
              onClick={() => handleProcessData()}
              disabled={isLoading}
              variant="outline"
              size="lg"
            >
              Process All Data
            </Button>

            <Button 
              onClick={() => handleGeneratePredictions()}
              disabled={isLoading}
              variant="outline"
              size="lg"
            >
              Generate Predictions
            </Button>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="scraped" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="scraped">
            Scraped Data ({scrapedData.length})
          </TabsTrigger>
          <TabsTrigger value="processed">
            Processed Matches ({processedMatches.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="scraped" className="space-y-4">
          {scrapedData.length > 0 ? (
            scrapedData.map((item) => (
              <Card key={item.id} className="border-success/20">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <Badge variant="outline" className="text-success border-success">
                      Scraped
                    </Badge>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="w-3 h-3" />
                      {new Date(item.scraped_at).toLocaleString()}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <p className="font-medium text-sm">Source URL:</p>
                    <p className="text-sm text-muted-foreground break-all">{item.source_url}</p>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button 
                      onClick={() => handleProcessData(item.id)}
                      size="sm"
                      variant="outline"
                      disabled={isLoading}
                    >
                      <Database className="w-3 h-3 mr-1" />
                      Process This Data
                    </Button>
                  </div>

                  <details className="text-sm">
                    <summary className="cursor-pointer font-medium">View Raw Data</summary>
                    <pre className="mt-2 p-3 bg-muted rounded text-xs overflow-auto max-h-40">
                      {JSON.stringify(item.payload, null, 2)}
                    </pre>
                  </details>
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">No scraped data yet. Start by adding source URLs above.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="processed" className="space-y-4">
          {processedMatches.length > 0 ? (
            processedMatches.map((match) => (
              <Card key={match.match_id} className="border-info/20">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-info border-info">
                        {match.sport || 'Unknown Sport'}
                      </Badge>
                      <Badge variant="secondary">
                        {match.league || 'Unknown League'}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Clock className="w-3 h-3" />
                      {new Date(match.match_date).toLocaleString()}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="text-center">
                    <h3 className="font-semibold text-lg">
                      {match.home_team} vs {match.away_team}
                    </h3>
                  </div>
                  
                  <Button 
                    onClick={() => handleGeneratePredictions(match.match_id)}
                    size="sm"
                    variant="outline"
                    disabled={isLoading}
                    className="w-full"
                  >
                    <Zap className="w-3 h-3 mr-1" />
                    Generate Predictions for This Match
                  </Button>

                  {match.stats && (
                    <details className="text-sm">
                      <summary className="cursor-pointer font-medium">View Match Stats</summary>
                      <pre className="mt-2 p-3 bg-muted rounded text-xs overflow-auto max-h-40">
                        {typeof match.stats === 'string' ? match.stats : JSON.stringify(match.stats, null, 2)}
                      </pre>
                    </details>
                  )}
                </CardContent>
              </Card>
            ))
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">No processed matches yet. Scrape and process some data first.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};