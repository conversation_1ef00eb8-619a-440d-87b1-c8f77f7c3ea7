
import React, { useState } from 'react';
import { Header } from '@/components/Header';
import HeroSection from '@/components/HeroSection';
import { CategorySelector } from '@/components/CategorySelector';
import { DashboardStats } from '@/components/DashboardStats';
import LivePredictions from '@/components/LivePredictions';

import PartnersSection from '@/components/PartnersSection';
import DownloadAppSection from '@/components/DownloadAppSection';
import MinimalFooter from '@/components/MinimalFooter';
import { AdSenseInline, AdSenseFooter } from '@/components/AdSense';
import { SEOBreadcrumb } from '@/components/SEOBreadcrumb';
import { SEOHead } from '@/components/SEOHead';
import { generateOrganizationSchema, generateWebAppSchema } from '@/utils/seo';

const Index: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');

  const breadcrumbs = [
    { name: 'Home', url: 'https://1300blk.online' }
  ];

  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
  };

  const structuredData = [
    generateOrganizationSchema(),
    generateWebAppSchema(),
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "ML Sports Intelligent Engine",
      "description": "AI-powered sports predictions for football, basketball, tennis and more with advanced analytics and real-time odds.",
      "url": "https://1300blk.online",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://1300blk.online/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
  ];

  return (
    <>
      <SEOHead
        title="ML Sports Intelligent Engine - AI-Powered Sports Betting Predictions"
        description="Get accurate AI-powered sports predictions for football, basketball, tennis and more. Advanced analytics, real-time odds, and expert insights to maximize your betting success."
        keywords="sports predictions, AI betting, machine learning sports, football predictions, basketball betting, tennis predictions, sports analytics, ML sports engine"
        canonical="https://1300blk.online"
        breadcrumbs={breadcrumbs}
        structuredData={structuredData}
      />

      <div className="min-h-screen bg-gradient-hero relative isolate">

        <Header />
        
        <SEOBreadcrumb breadcrumbs={breadcrumbs} />
        
        <main>
          <div data-aos="fade-up" data-aos-duration="1000">
            <HeroSection />
          </div>

          <div className="container mx-auto px-4 py-8 relative z-30">
            <div data-aos="fade-up" data-aos-delay="200">
              <CategorySelector onCategoryChange={handleCategoryChange} />
            </div>
            <div data-aos="fade-up" data-aos-delay="300">
              <AdSenseInline />
            </div>
            <div data-aos="fade-up" data-aos-delay="400">
              <DashboardStats />
            </div>
            <div data-aos="fade-up" data-aos-delay="500">
              <AdSenseInline />
            </div>
            <div data-aos="fade-up" data-aos-delay="600">
              <LivePredictions />
            </div>
            <div data-aos="fade-up" data-aos-delay="700">
              <AdSenseInline />
            </div>
            <div data-aos="fade-up" data-aos-delay="800">
              <PartnersSection />
            </div>
            <div data-aos="fade-up" data-aos-delay="900">
              <DownloadAppSection />
            </div>
          </div>
        </main>
        
        <MinimalFooter />
        <AdSenseFooter />
      </div>
    </>
  );
};

export default Index;
