import React from 'react';
import { Header } from '@/components/Header';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Settings, Server, Database, Shield, Bell, Globe } from 'lucide-react';

const AdminSettings: React.FC = () => {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-2">System Settings</h1>
          <p className="text-muted-foreground">Configure platform settings and preferences</p>
        </div>

        <div className="grid gap-6">
          <Card className="border-border shadow-carved-deep bg-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Server className="h-5 w-5" />
                Server Configuration
              </CardTitle>
              <CardDescription>Configure server and performance settings</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border border-border rounded-lg shadow-carved bg-card">
                  <h3 className="font-medium mb-2">CPU Usage Limit</h3>
                  <div className="text-2xl font-bold text-primary mb-2">75%</div>
                  <Button variant="outline" size="sm">Adjust</Button>
                </div>
                <div className="p-4 border border-border rounded-lg shadow-carved bg-card">
                  <h3 className="font-medium mb-2">Memory Allocation</h3>
                  <div className="text-2xl font-bold text-primary mb-2">16GB</div>
                  <Button variant="outline" size="sm">Configure</Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-border shadow-carved-deep bg-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Database Settings
              </CardTitle>
              <CardDescription>Database configuration and maintenance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-4 border border-border rounded-lg shadow-carved bg-card">
                  <div>
                    <h3 className="font-medium">Backup Schedule</h3>
                    <p className="text-sm text-muted-foreground">Daily at 2:00 AM</p>
                  </div>
                  <Button variant="outline">Configure</Button>
                </div>
                <div className="flex justify-between items-center p-4 border border-border rounded-lg shadow-carved bg-card">
                  <div>
                    <h3 className="font-medium">Connection Pool</h3>
                    <p className="text-sm text-muted-foreground">Max 100 connections</p>
                  </div>
                  <Button variant="outline">Adjust</Button>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="border-border shadow-carved-deep bg-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Security Settings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Two-Factor Authentication</span>
                    <Button variant="outline" size="sm">Enable</Button>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Password Policy</span>
                    <Button variant="outline" size="sm">Update</Button>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Session Timeout</span>
                    <Button variant="outline" size="sm">Configure</Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-border shadow-carved-deep bg-card">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bell className="h-5 w-5" />
                  Notification Settings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Email Notifications</span>
                    <Button variant="outline" size="sm">Configure</Button>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Push Notifications</span>
                    <Button variant="outline" size="sm">Setup</Button>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm">Alert Thresholds</span>
                    <Button variant="outline" size="sm">Set</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminSettings;