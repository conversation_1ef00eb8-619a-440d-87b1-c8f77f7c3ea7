import { ScrapingManager } from '../scraping/scrapingManager';
import { PredictionEngine } from '../ml/predictionEngine';
import { prisma } from '@/config/database';
import { logger } from '@/utils/logger';
import axios from 'axios';

interface PipelineConfig {
  enablePythonService: boolean;
  pythonServiceUrl: string;
  enableRealTimePredictions: boolean;
  enableDataValidation: boolean;
  enableNotifications: boolean;
  maxRetries: number;
  retryDelay: number;
}

interface PipelineResult {
  success: boolean;
  matchesProcessed: number;
  predictionsGenerated: number;
  errors: string[];
  duration: number;
  timestamp: Date;
}

export class DataPipeline {
  private scrapingManager: ScrapingManager;
  private predictionEngine: PredictionEngine;
  private config: PipelineConfig;

  constructor() {
    this.scrapingManager = new ScrapingManager();
    this.predictionEngine = new PredictionEngine();
    this.config = {
      enablePythonService: process.env.PY_SERVICE_URL ? true : false,
      pythonServiceUrl: process.env.PY_SERVICE_URL || 'http://localhost:8088',
      enableRealTimePredictions: true,
      enableDataValidation: true,
      enableNotifications: true,
      maxRetries: 3,
      retryDelay: 5000,
    };
  }

  /**
   * Execute the complete data pipeline: Scrape → Process → Predict → Store
   */
  async executePipeline(sport: string, league?: string): Promise<PipelineResult> {
    const startTime = Date.now();
    const result: PipelineResult = {
      success: false,
      matchesProcessed: 0,
      predictionsGenerated: 0,
      errors: [],
      duration: 0,
      timestamp: new Date(),
    };

    logger.info(`Starting data pipeline for ${sport}/${league}`);

    try {
      // Step 1: Scrape data from all providers
      const scrapingResult = await this.scrapeData(sport, league);
      if (!scrapingResult.success) {
        result.errors.push(`Scraping failed: ${scrapingResult.error}`);
        return result;
      }

      result.matchesProcessed = scrapingResult.totalMatches;

      // Step 2: Process and validate data
      if (this.config.enableDataValidation) {
        await this.validateAndCleanData(sport, league);
      }

      // Step 3: Generate predictions using multiple methods
      const predictionResult = await this.generatePredictions(sport, league);
      result.predictionsGenerated = predictionResult.count;

      if (predictionResult.errors.length > 0) {
        result.errors.push(...predictionResult.errors);
      }

      // Step 4: Post-processing and notifications
      await this.postProcessing(sport, league, result);

      result.success = true;
      result.duration = Date.now() - startTime;

      logger.info(`Pipeline completed successfully: ${result.matchesProcessed} matches, ${result.predictionsGenerated} predictions in ${result.duration}ms`);

    } catch (error) {
      result.errors.push(`Pipeline error: ${error}`);
      result.duration = Date.now() - startTime;
      logger.error('Data pipeline failed:', error);
    }

    return result;
  }

  /**
   * Scrape data from all available providers
   */
  private async scrapeData(sport: string, league?: string): Promise<any> {
    try {
      // Use the existing scraping manager
      const result = await this.scrapingManager.scrapeAllProviders(sport, league);
      
      // Also try Python service if available
      if (this.config.enablePythonService) {
        try {
          await this.scrapeWithPythonService(sport, league);
        } catch (error) {
          logger.warn('Python service scraping failed:', error);
        }
      }

      return result;
    } catch (error) {
      logger.error('Scraping failed:', error);
      throw error;
    }
  }

  /**
   * Use Python service for additional scraping and ML predictions
   */
  private async scrapeWithPythonService(sport: string, league?: string): Promise<void> {
    try {
      const response = await axios.post(`${this.config.pythonServiceUrl}/pipeline/scrape_predict`, {
        provider: 'espn',
        sport,
        league,
      }, {
        timeout: 30000,
      });

      if (response.data.success) {
        logger.info(`Python service processed ${response.data.matches?.length || 0} matches`);
      }
    } catch (error) {
      logger.warn('Python service request failed:', error);
      throw error;
    }
  }

  /**
   * Validate and clean scraped data
   */
  private async validateAndCleanData(sport: string, league?: string): Promise<void> {
    try {
      // Get recent matches that need validation
      const matches = await prisma.sportsData.findMany({
        where: {
          sport,
          league,
          createdAt: {
            gte: new Date(Date.now() - 60 * 60 * 1000), // Last hour
          },
        },
      });

      for (const match of matches) {
        // Validate match data
        const isValid = this.validateMatchData(match);
        
        if (!isValid) {
          logger.warn(`Invalid match data found: ${match.id}`);
          
          // Mark as invalid or attempt to fix
          await prisma.sportsData.update({
            where: { id: match.id },
            data: {
              status: 'invalid',
            },
          });
        }

        // Clean and normalize data
        await this.normalizeMatchData(match);
      }

      logger.info(`Validated ${matches.length} matches for ${sport}/${league}`);
    } catch (error) {
      logger.error('Data validation failed:', error);
    }
  }

  /**
   * Validate individual match data
   */
  private validateMatchData(match: any): boolean {
    // Check required fields
    if (!match.homeTeam || !match.awayTeam || !match.matchDate) {
      return false;
    }

    // Check date validity
    const matchDate = new Date(match.matchDate);
    if (isNaN(matchDate.getTime())) {
      return false;
    }

    // Check if match is not too far in the past or future
    const now = new Date();
    const daysDiff = Math.abs(matchDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24);
    if (daysDiff > 365) {
      return false;
    }

    return true;
  }

  /**
   * Normalize and clean match data
   */
  private async normalizeMatchData(match: any): Promise<void> {
    try {
      const updates: any = {};

      // Normalize team names
      if (match.homeTeam) {
        updates.homeTeam = this.normalizeTeamName(match.homeTeam);
      }
      if (match.awayTeam) {
        updates.awayTeam = this.normalizeTeamName(match.awayTeam);
      }

      // Normalize odds format
      if (match.odds) {
        try {
          const odds = typeof match.odds === 'string' ? JSON.parse(match.odds) : match.odds;
          updates.odds = JSON.stringify(this.normalizeOdds(odds));
        } catch (error) {
          logger.warn(`Failed to normalize odds for match ${match.id}`);
        }
      }

      // Update if there are changes
      if (Object.keys(updates).length > 0) {
        await prisma.sportsData.update({
          where: { id: match.id },
          data: updates,
        });
      }
    } catch (error) {
      logger.warn(`Failed to normalize match data for ${match.id}:`, error);
    }
  }

  /**
   * Normalize team names for consistency
   */
  private normalizeTeamName(teamName: string): string {
    return teamName
      .trim()
      .replace(/\s+/g, ' ')
      .replace(/[^\w\s]/g, '')
      .toLowerCase()
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  /**
   * Normalize odds format
   */
  private normalizeOdds(odds: any): any {
    if (!odds || typeof odds !== 'object') {
      return odds;
    }

    const normalized: any = {};

    // Standardize odds format
    for (const [key, value] of Object.entries(odds)) {
      if (typeof value === 'number') {
        normalized[key] = value;
      } else if (typeof value === 'string') {
        const numValue = parseFloat(value);
        if (!isNaN(numValue)) {
          normalized[key] = numValue;
        }
      }
    }

    return normalized;
  }

  /**
   * Generate predictions using multiple methods
   */
  private async generatePredictions(sport: string, league?: string): Promise<{ count: number; errors: string[] }> {
    const result: { count: number; errors: string[] } = { count: 0, errors: [] };

    try {
      // Get matches that need predictions
      const matches = await prisma.sportsData.findMany({
        where: {
          sport,
          league,
          status: {
            in: ['upcoming', 'scheduled'],
          },
          matchDate: {
            gte: new Date(), // Future matches only
          },
        },
        take: 100, // Limit to avoid overwhelming
      });

      for (const match of matches) {
        try {
          // Check if predictions already exist
          const existingPrediction = await prisma.prediction.findFirst({
            where: {
              sport: match.sport,
              league: match.league,
              homeTeam: match.homeTeam,
              awayTeam: match.awayTeam,
              matchDate: match.matchDate,
            },
          });

          if (!existingPrediction) {
            // Generate predictions
            // Convert database match to MatchData format
            const matchData = {
              matchId: match.externalId || match.id,
              externalId: match.externalId || undefined,
              sport: match.sport,
              league: match.league,
              homeTeam: match.homeTeam,
              awayTeam: match.awayTeam,
              matchDate: match.matchDate,
              odds: match.odds ? JSON.parse(match.odds) : undefined,
              stats: match.stats ? JSON.parse(match.stats) : undefined,
              source: match.source,
              status: match.status,
              homeScore: match.homeScore || undefined,
              awayScore: match.awayScore || undefined,
            };

            const predictions = await this.predictionEngine.generatePredictions(matchData);

            // Save predictions
            for (const prediction of predictions) {
              await prisma.prediction.create({
                data: {
                  sport: match.sport,
                  league: match.league,
                  homeTeam: match.homeTeam,
                  awayTeam: match.awayTeam,
                  matchDate: match.matchDate,
                  market: prediction.market,
                  pick: prediction.pick,
                  confidence: prediction.confidence,
                  odds: prediction.odds,
                  // edge: prediction.expectedValue, // Field doesn't exist in schema
                  expectedValue: prediction.expectedValue,
                  factors: JSON.stringify(prediction.factors),
                  source: 'DATA_PIPELINE',
                  status: 'upcoming',
                  isPremium: prediction.confidence >= 75,
                },
              });
              result.count++;
            }
          }
        } catch (error) {
          result.errors.push(`Failed to generate prediction for match ${match.id}: ${(error as Error).message}`);
        }
      }

      logger.info(`Generated ${result.count} predictions for ${sport}/${league}`);
    } catch (error) {
      result.errors.push(`Prediction generation failed: ${(error as Error).message}`);
      logger.error('Prediction generation failed:', error);
    }

    return result;
  }

  /**
   * Post-processing tasks
   */
  private async postProcessing(sport: string, league: string | undefined, result: PipelineResult): Promise<void> {
    try {
      // Log pipeline execution
      await prisma.auditLog.create({
        data: {
          action: 'DATA_PIPELINE_EXECUTED',
          resource: 'automation',
          details: JSON.stringify({
            sport,
            league,
            matchesProcessed: result.matchesProcessed,
            predictionsGenerated: result.predictionsGenerated,
            duration: result.duration,
            errors: result.errors,
          }),
        },
      });

      // Send notifications if enabled
      if (this.config.enableNotifications && result.predictionsGenerated > 0) {
        await this.sendNotifications(sport, league, result);
      }

      // Update statistics
      await this.updateStatistics(sport, league, result);

    } catch (error) {
      logger.error('Post-processing failed:', error);
    }
  }

  /**
   * Send notifications about new predictions
   */
  private async sendNotifications(sport: string, league: string | undefined, result: PipelineResult): Promise<void> {
    try {
      // This could integrate with email, SMS, or push notification services
      logger.info(`Notification: Generated ${result.predictionsGenerated} new predictions for ${sport}/${league}`);
      
      // Example: Could send to admin users or subscribers
      // await emailService.sendPredictionAlert(sport, league, result.predictionsGenerated);
    } catch (error) {
      logger.error('Failed to send notifications:', error);
    }
  }

  /**
   * Update system statistics
   */
  private async updateStatistics(sport: string, league: string | undefined, result: PipelineResult): Promise<void> {
    try {
      // Update global settings with latest stats
      await prisma.globalSettings.upsert({
        where: { key: 'last_pipeline_execution' },
        update: {
          value: `Last executed: ${result.matchesProcessed} matches, ${result.predictionsGenerated} predictions at ${new Date().toISOString()}`,
        },
        create: {
          key: 'last_pipeline_execution',
          value: `Last executed: ${result.matchesProcessed} matches, ${result.predictionsGenerated} predictions at ${new Date().toISOString()}`,
        },
      });
    } catch (error) {
      logger.error('Failed to update statistics:', error);
    }
  }

  /**
   * Get pipeline status and metrics
   */
  async getStatus(): Promise<any> {
    try {
      const recentJobs = await prisma.scrapingJob.findMany({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
          },
        },
        orderBy: { createdAt: 'desc' },
        take: 10,
      });

      const recentPredictions = await prisma.prediction.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000),
          },
        },
      });

      return {
        recentJobs: recentJobs.length,
        recentPredictions,
        lastExecution: recentJobs[0]?.createdAt,
        pythonServiceEnabled: this.config.enablePythonService,
        pythonServiceUrl: this.config.pythonServiceUrl,
      };
    } catch (error) {
      logger.error('Failed to get pipeline status:', error);
      return { error: 'Failed to get status' };
    }
  }
}

// Export singleton instance
export const dataPipeline = new DataPipeline();
