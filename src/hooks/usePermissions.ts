import { useAuth } from '@/contexts/AuthContext'

export const usePermissions = () => {
  const { user, isAdmin, isSuperAdmin } = useAuth()

  const canManageUsers = isSuperAdmin
  const canManagePredictions = isAdmin
  const canManageSettings = isAdmin
  const canViewAnalytics = isAdmin
  const canManageGlobalSettings = isSuperAdmin
  const canManagePayments = isAdmin
  const canAccessPremiumFeatures = user?.subscriptionStatus === 'ACTIVE' || isAdmin

  return {
    canManageUsers,
    canManagePredictions,
    canManageSettings,
    canViewAnalytics,
    canManageGlobalSettings,
    canManagePayments,
    canAccessPremiumFeatures,
  }
}