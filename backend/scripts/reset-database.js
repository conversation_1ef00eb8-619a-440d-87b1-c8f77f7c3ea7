const { execSync } = require('child_process');
const path = require('path');

console.log('🔄 Resetting database...');

try {
  // Reset the database
  console.log('🗑️  Dropping and recreating database...');
  execSync('npx prisma migrate reset --force', { stdio: 'inherit', cwd: path.join(__dirname, '..') });

  console.log('✅ Database reset completed successfully!');
  console.log('');
  console.log('The database has been reset and seeded with fresh data.');

} catch (error) {
  console.error('❌ Database reset failed:', error.message);
  process.exit(1);
}
