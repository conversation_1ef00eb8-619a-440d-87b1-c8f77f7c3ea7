
import { useState, useEffect } from 'react'
import { apiService } from '@/services/api'

interface LivePrediction {
  id: string
  sport: string
  league: string
  home_team: string
  away_team: string
  match_date: string
  market: string
  pick: string
  confidence: number
  odds: number
  edge: number
  expected_value: number
  source: string
  status: string
  is_premium: boolean
  factors: any
  created_at: string
}

export const useLivePredictions = () => {
  const [predictions, setPredictions] = useState<LivePrediction[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchPredictions = async () => {
    try {
      setLoading(true)
      const response = await apiService.getAllPredictions()

      if (!response.success) throw new Error(response.error)

      // Map API data to LivePrediction format
      const mappedPredictions = (response.data?.predictions || []).map(pred => ({
        id: pred.id,
        sport: pred.sport,
        league: pred.league,
        home_team: pred.homeTeam,
        away_team: pred.awayTeam,
        match_date: pred.matchDate,
        market: pred.market,
        pick: pred.pick,
        confidence: pred.confidence,
        odds: pred.odds,
        expected_value: pred.expectedValue,
        status: 'upcoming',
        is_premium: false,
        factors: pred.factors,
        created_at: pred.createdAt
      }));

      // Remove duplicates based on unique combination of teams, market, sport, and date
      const uniquePredictions = mappedPredictions.filter((prediction, index, self) => {
        const key = `${prediction.sport}-${prediction.league}-${prediction.home_team}-${prediction.away_team}-${prediction.market}-${prediction.match_date}`;
        return index === self.findIndex(p =>
          `${p.sport}-${p.league}-${p.home_team}-${p.away_team}-${p.market}-${p.match_date}` === key
        );
      });

      setPredictions(uniquePredictions)
    } catch (err) {
      console.error('Error fetching live predictions:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch predictions')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchPredictions()

    // Set up polling for real-time updates (since we don't have WebSocket yet)
    const interval = setInterval(() => {
      fetchPredictions()
    }, 30000) // Poll every 30 seconds

    return () => {
      clearInterval(interval)
    }
  }, [])

  return { predictions, loading, error, refetch: fetchPredictions }
}
