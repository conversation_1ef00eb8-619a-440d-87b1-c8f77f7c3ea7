import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { toast } from '@/components/ui/use-toast';
import { apiService as api } from '@/services/api';

interface ScraperResult {
  success: boolean;
  matches_scraped?: number;
  predictions_generated?: number;
  error?: string;
}

export const ScraperTestDashboard = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [testResults, setTestResults] = useState<any>(null);
  const [scraperResults, setScraperResults] = useState<ScraperResult | null>(null);
  const [predictionResults, setPredictionResults] = useState<ScraperResult | null>(null);
  const [selectedSport, setSelectedSport] = useState('football');
  const [selectedLeague, setSelectedLeague] = useState('nfl');

  const runScraperTest = async () => {
    setIsLoading(true);
    try {
      const response = await api.startScraping('espn', selectedSport, selectedLeague);

      if (!response.success) {
        toast({
          title: 'Test Error',
          description: response.error || 'Scraping test failed',
          variant: 'destructive'
        });
      } else {
        setTestResults(response.data);
        toast({
          title: 'Test Completed',
          description: 'Check the results below'
        });
      }
    } catch (error: any) {
      toast({
        title: 'Test Failed',
        description: error.message,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const runDataScraper = async () => {
    setIsLoading(true);
    try {
      const response = await api.startComprehensiveScraping(selectedSport, selectedLeague);

      if (!response.success) {
        toast({
          title: 'Scraper Error',
          description: response.error || 'Scraping failed',
          variant: 'destructive'
        });
      } else {
        setScraperResults(response.data);
        toast({
          title: 'Scraping Completed',
          description: `Scraped ${response.data?.totalMatches || 0} matches`
        });
      }
    } catch (error: any) {
      toast({
        title: 'Scraping Failed',
        description: error.message,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const generatePredictions = async () => {
    setIsLoading(true);
    try {
      // First get sports data to generate predictions for
      const sportsDataResponse = await api.getSportsData({
        sport: selectedSport,
        league: selectedLeague,
        status: 'UPCOMING',
        limit: 10
      });

      if (!sportsDataResponse.success || !sportsDataResponse.data?.matches.length) {
        toast({
          title: 'No Data',
          description: 'No upcoming matches found to generate predictions for',
          variant: 'destructive'
        });
        return;
      }

      // Generate predictions for the first match as an example
      const firstMatch = sportsDataResponse.data.matches[0];
      const response = await api.generatePredictions(firstMatch.id);

      if (!response.success) {
        toast({
          title: 'Prediction Error',
          description: response.error || 'Prediction generation failed',
          variant: 'destructive'
        });
      } else {
        setPredictionResults(response.data);
        toast({
          title: 'Predictions Generated',
          description: `Generated ${response.data?.length || 0} predictions`
        });
      }
    } catch (error: any) {
      toast({
        title: 'Prediction Failed',
        description: error.message,
        variant: 'destructive'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const viewSportsData = async () => {
    try {
      const response = await apiService.getSportsData();

      if (!response.success) {
        toast({
          title: 'API Error',
          description: response.error,
          variant: 'destructive'
        });
      } else {
        console.log('Latest sports data:', response.data);
        toast({
          title: 'Data Retrieved',
          description: `Found ${response.data?.length || 0} recent records (check console for details)`
        });
      }
    } catch (error: any) {
      toast({
        title: 'Query Failed',
        description: error.message,
        variant: 'destructive'
      });
    }
  };

  const viewPredictions = async () => {
    try {
      const response = await apiService.getAllPredictions();

      if (!response.success) {
        toast({
          title: 'API Error',
          description: response.error,
          variant: 'destructive'
        });
      } else {
        console.log('Latest predictions:', response.data);
        toast({
          title: 'Predictions Retrieved',
          description: `Found ${response.data?.predictions?.length || 0} recent predictions (check console for details)`
        });
      }
    } catch (error: any) {
      toast({
        title: 'Query Failed',
        description: error.message,
        variant: 'destructive'
      });
    }
  };

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Sports Data Scraper & Prediction System</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium">Sport</label>
              <Select value={selectedSport} onValueChange={setSelectedSport}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="football">Football</SelectItem>
                  <SelectItem value="basketball">Basketball</SelectItem>
                  <SelectItem value="baseball">Baseball</SelectItem>
                  <SelectItem value="hockey">Hockey</SelectItem>
                  <SelectItem value="soccer">Soccer</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium">League</label>
              <Select value={selectedLeague} onValueChange={setSelectedLeague}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {selectedSport === 'football' && (
                    <SelectItem value="nfl">NFL</SelectItem>
                  )}
                  {selectedSport === 'basketball' && (
                    <SelectItem value="nba">NBA</SelectItem>
                  )}
                  {selectedSport === 'baseball' && (
                    <SelectItem value="mlb">MLB</SelectItem>
                  )}
                  {selectedSport === 'hockey' && (
                    <SelectItem value="nhl">NHL</SelectItem>
                  )}
                  {selectedSport === 'soccer' && (
                    <>
                      <SelectItem value="mls">MLS</SelectItem>
                      <SelectItem value="epl">EPL</SelectItem>
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Button 
              onClick={runScraperTest}
              disabled={isLoading}
              variant="outline"
            >
              Test APIs
            </Button>
            <Button 
              onClick={runDataScraper}
              disabled={isLoading}
            >
              Scrape Data
            </Button>
            <Button 
              onClick={generatePredictions}
              disabled={isLoading}
              variant="secondary"
            >
              Generate Predictions
            </Button>
            <Button 
              onClick={viewSportsData}
              variant="ghost"
            >
              View Sports Data
            </Button>
          </div>

          <Button 
            onClick={viewPredictions}
            variant="ghost"
            className="w-full"
          >
            View Predictions
          </Button>
        </CardContent>
      </Card>

      {testResults && (
        <Card>
          <CardHeader>
            <CardTitle>API Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Badge variant={testResults.success ? "default" : "destructive"}>
                  {testResults.success ? "Success" : "Failed"}
                </Badge>
                <span className="text-sm">{testResults.message}</span>
              </div>
              {testResults.apis_configured && (
                <div className="grid grid-cols-3 gap-4 mt-4">
                  <div className="text-center">
                    <Badge variant={testResults.apis_configured.espn_api ? "default" : "outline"}>
                      ESPN API: {testResults.apis_configured.espn_api ? "✓" : "✗"}
                    </Badge>
                  </div>
                  <div className="text-center">
                    <Badge variant={testResults.apis_configured.odds_api ? "default" : "outline"}>
                      Odds API: {testResults.apis_configured.odds_api ? "✓" : "✗"}
                    </Badge>
                  </div>
                  <div className="text-center">
                    <Badge variant={testResults.apis_configured.sports_radar ? "default" : "outline"}>
                      SportsRadar: {testResults.apis_configured.sports_radar ? "✓" : "✗"}
                    </Badge>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {scraperResults && (
        <Card>
          <CardHeader>
            <CardTitle>Scraper Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Badge variant={scraperResults.success ? "default" : "destructive"}>
                {scraperResults.success ? "Success" : "Failed"}
              </Badge>
              {scraperResults.matches_scraped && (
                <p>Matches Scraped: <strong>{scraperResults.matches_scraped}</strong></p>
              )}
              {scraperResults.error && (
                <p className="text-red-500">Error: {scraperResults.error}</p>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {predictionResults && (
        <Card>
          <CardHeader>
            <CardTitle>Prediction Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Badge variant={predictionResults.success ? "default" : "destructive"}>
                {predictionResults.success ? "Success" : "Failed"}
              </Badge>
              {predictionResults.predictions_generated && (
                <p>Predictions Generated: <strong>{predictionResults.predictions_generated}</strong></p>
              )}
              {predictionResults.error && (
                <p className="text-red-500">Error: {predictionResults.error}</p>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};