
import { useState, useEffect, useRef } from "react";
import { <PERSON>, CardHeader, CardT<PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Filter, Search, Download, TrendingUp } from "lucide-react";
import { cn } from "@/lib/utils";
import { api, type PredictionData } from "@/services/api";
import { useToast } from "@/hooks/use-toast";
import { NeumorphicCard } from "@/components/ui/neumorphic-card";
import { useAOS } from "@/hooks/useAOS";

const TableRowSkeleton = () => (
  <tr className="border-b border-border/50">
    <td className="p-4"><Skeleton className="h-4 w-12" /></td>
    <td className="p-4">
      <Skeleton className="h-4 w-32 mb-1" />
      <Skeleton className="h-3 w-24" />
    </td>
    <td className="p-4"><Skeleton className="h-4 w-24" /></td>
    <td className="p-4"><Skeleton className="h-4 w-20" /></td>
    <td className="p-4"><Skeleton className="h-4 w-10" /></td>
    <td className="p-4"><Skeleton className="h-4 w-12" /></td>
    <td className="p-4"><Skeleton className="h-4 w-10" /></td>
    <td className="p-4"><Skeleton className="h-6 w-12 rounded" /></td>
    <td className="p-4"><Skeleton className="h-6 w-16 rounded" /></td>
  </tr>
);

interface PredictionsTableProps {
  loading?: boolean;
}

export const PredictionsTable = ({ loading = false }: PredictionsTableProps) => {
  const [sportFilter, setSportFilter] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [predictions, setPredictions] = useState<PredictionData[]>([]);
  const [isLoading, setIsLoading] = useState(loading);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const seededRef = useRef(false);
   const { ref: containerRef, isVisible } = useAOS({ threshold: 0.1, delay: 150 });
  
  useEffect(() => {
    fetchPredictions();

    // Set up polling for live updates every 30 seconds
    const interval = setInterval(() => {
      fetchPredictions();
    }, 30000);

    return () => {
      clearInterval(interval);
    };
  }, [sportFilter]);
 
  const fetchPredictions = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await api.getTodaysPredictions({
        sport: sportFilter,
        limit: 50
      });

      if (response.success && response.data) {
        // Remove duplicates here as well
        const uniquePredictions = response.data.predictions.filter((prediction, index, self) => {
          const key = `${prediction.sport}-${prediction.league}-${prediction.homeTeam}-${prediction.awayTeam}-${prediction.market}-${prediction.matchDate}`;
          return index === self.findIndex(p =>
            `${p.sport}-${p.league}-${p.homeTeam}-${p.awayTeam}-${p.market}-${p.matchDate}` === key
          );
        });
        setPredictions(uniquePredictions);

        // If empty once, trigger scraper and retry
        if (uniquePredictions.length === 0 && !seededRef.current) {
          seededRef.current = true;
          toast({ title: 'Fetching live fixtures', description: 'Seeding fresh data. This may take a few seconds...' });
          try {
            await apiService.processSportsData({ processAll: true });
            const retry = await apiService.getTodaysPredictions({ sport: sportFilter, limit: 50 });
            if (retry.success && retry.data) {
              const uniqueRetry = retry.data.filter((prediction, index, self) => {
                const key = `${prediction.sport}-${prediction.league}-${prediction.home_team}-${prediction.away_team}-${prediction.market}-${prediction.match_date}`;
                return index === self.findIndex(p => `${p.sport}-${p.league}-${p.home_team}-${p.away_team}-${p.market}-${p.match_date}` === key);
              });
              setPredictions(uniqueRetry);
            }
          } catch (e) {
            console.error('Scraper trigger failed', e);
          }
        }
      } else {
        setError('Failed to fetch predictions');
        setPredictions([]);
      }
    } catch (err) {
      setError('Failed to fetch predictions. Using fallback data.');
      console.error('Error fetching predictions:', err);
      setPredictions([]);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredPredictions = predictions.filter(prediction => {
    const matchesSearch = searchQuery === '' ||
      prediction.homeTeam.toLowerCase().includes(searchQuery.toLowerCase()) ||
      prediction.awayTeam.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesSearch;
  });
  
  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 75) return 'text-confidence-high';
    if (confidence >= 60) return 'text-confidence-medium';
    return 'text-confidence-low';
  };

  const getStatusBadge = (status: string) => {
    const config = {
      upcoming: { variant: 'default' as const, className: '' },
      won: { variant: 'default' as const, className: 'bg-success text-success-foreground' }, 
      lost: { variant: 'destructive' as const, className: '' },
      void: { variant: 'secondary' as const, className: '' }
    };
    
    const { variant, className } = config[status as keyof typeof config] || config.upcoming;
    
    return <Badge variant={variant} className={className}>
      {status.charAt(0).toUpperCase() + status.slice(1)}
    </Badge>;
  };

  const formatTime = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch {
      return 'TBD';
    }
  };

  return (
    <div ref={containerRef} className={`aos-card relative z-20 ${isVisible ? 'animate' : ''}`}>
      <NeumorphicCard className="shadow-glow" pulse>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-bruno drop-shadow-md">
            <TrendingUp className="h-5 w-5" />
            Live Predictions
            <Badge variant="secondary" className="ml-auto neumorphic border-none">
              {filteredPredictions.length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          {/* Header & Filters */}
          <div className="p-6 border-b border-border/50">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                <h2 className="text-xl font-semibold text-foreground">Today's Predictions</h2>
                <p className="text-sm text-muted-foreground">AI-powered predictions with confidence scores</p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="flex items-center space-x-2 bg-muted/50 rounded-lg px-3 py-2">
                  <Search className="w-4 h-4 text-muted-foreground" />
                  <Input 
                    placeholder="Search teams..." 
                    className="border-0 bg-transparent text-sm focus-visible:ring-0 p-0 min-w-[150px]"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                  />
                </div>
                
                <Select value={sportFilter} onValueChange={setSportFilter}>
                  <SelectTrigger className="w-[130px]">
                    <SelectValue placeholder="Sport" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sports</SelectItem>
                    <SelectItem value="football">Football</SelectItem>
                    <SelectItem value="basketball">Basketball</SelectItem>
                    <SelectItem value="tennis">Tennis</SelectItem>
                    <SelectItem value="baseball">Baseball</SelectItem>
                  </SelectContent>
                </Select>

                <Button variant="outline" size="sm">
                  <Download className="w-4 h-4 mr-2" />
                  Export
                </Button>
              </div>
            </div>
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-muted/30">
                <tr className="border-b border-border">
                  <th className="text-left p-4 font-medium text-muted-foreground">Time</th>
                  <th className="text-left p-4 font-medium text-muted-foreground">Match</th>
                  <th className="text-left p-4 font-medium text-muted-foreground">Market</th>
                  <th className="text-left p-4 font-medium text-muted-foreground">Pick</th>
                  <th className="text-left p-4 font-medium text-muted-foreground">Confidence</th>
                  <th className="text-left p-4 font-medium text-muted-foreground">Odds</th>
                  <th className="text-left p-4 font-medium text-muted-foreground">Edge</th>
                  <th className="text-left p-4 font-medium text-muted-foreground">Source</th>
                  <th className="text-left p-4 font-medium text-muted-foreground">Status</th>
                </tr>
              </thead>
              <tbody>
                {isLoading ? (
                  // Show skeleton rows when loading
                  Array.from({ length: 5 }).map((_, index) => (
                    <TableRowSkeleton key={index} />
                  ))
                ) : filteredPredictions.length === 0 ? (
                  <tr>
                    <td colSpan={9} className="p-8 text-center text-muted-foreground">
                      {error ? error : 'No predictions available for the selected filters.'}
                    </td>
                  </tr>
                ) : (
                  filteredPredictions.map((prediction) => (
                    <tr key={prediction.id} className="border-b border-border/50 hover:bg-muted/20 transition-colors">
                      <td className="p-4 text-sm text-foreground font-mono">{formatTime(prediction.matchDate)}</td>
                      <td className="p-4">
                        <div>
                          <div className="text-sm font-medium text-foreground">
                            {prediction.homeTeam} vs {prediction.awayTeam}
                          </div>
                          <div className="text-xs text-muted-foreground">{prediction.league}</div>
                        </div>
                      </td>
                      <td className="p-4 text-sm text-muted-foreground">{prediction.market}</td>
                      <td className="p-4 text-sm font-medium text-foreground">{prediction.pick}</td>
                      <td className="p-4">
                        <span className={cn("text-sm font-semibold", getConfidenceColor(prediction.confidence))}>
                          {prediction.confidence}%
                        </span>
                      </td>
                      <td className="p-4 text-sm font-medium text-foreground">{prediction.odds}</td>
                      <td className="p-4">
                        <span className={cn("text-sm font-semibold",
                          prediction.edge && prediction.edge > 0 ? 'text-success' : 'text-destructive'
                        )}>
                          {prediction.edge && prediction.edge > 0 ? '+' : ''}{prediction.edge || 0}%
                        </span>
                      </td>
                      <td className="p-4">
                        <Badge variant={prediction.source === 'ML' ? 'secondary' : 'default'} className="text-xs">
                          {prediction.source === 'ML' ? 'AI' : 'Expert'}
                        </Badge>
                      </td>
                      <td className="p-4">{getStatusBadge(prediction.status)}</td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </NeumorphicCard>
    </div>
  );
};
