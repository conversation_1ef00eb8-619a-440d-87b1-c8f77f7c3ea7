import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, Download, Globe, Zap, Database, Clock } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { apiService } from '@/services/api';

interface ScraperResult {
  success: boolean;
  data?: any;
  error?: string;
  cached?: boolean;
  timestamp?: number;
  metadata?: {
    title?: string;
    description?: string;
    url: string;
    status: number;
  };
}

export const WebScraperInterface: React.FC = () => {
  const { toast } = useToast();
  const [url, setUrl] = useState('');
  const [extractRules, setExtractRules] = useState('');
  const [useCache, setUseCache] = useState(true);
  const [timeout, setTimeout] = useState(15000);
  const [waitFor, setWaitFor] = useState(1000);
  const [type, setType] = useState<'html' | 'json' | 'text'>('html');
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState<ScraperResult | null>(null);

  const handleScrape = async () => {
    if (!url.trim()) {
      toast({
        title: "Error",
        description: "Please enter a valid URL",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);
    setResult(null);

    try {
      let parsedRules = {};
      if (extractRules.trim()) {
        try {
          parsedRules = JSON.parse(extractRules);
        } catch {
          toast({
            title: "Invalid JSON",
            description: "Please provide valid JSON extraction rules",
            variant: "destructive",
          });
          setIsLoading(false);
          return;
        }
      }

      // Prefer the lightweight 'scrape' function for basic HTML extraction (faster cold starts)
      // Fall back to 'web-scraper' when custom extractRules provided or non-HTML modes are requested
      const useSimpleScraper = type === 'html' && Object.keys(parsedRules).length === 0;

      const fnName = useSimpleScraper ? 'scrape' : 'web-scraper';
      const payload = useSimpleScraper
        ? { url }
        : { url, type, extractRules: parsedRules, useCache, timeout, waitFor };

      const response = await apiService.scrapeWebData(payload);

      if (!response.success) {
        throw new Error(response.error);
      }

      setResult(response.data);

      if (response.success) {
        toast({
          title: "Success",
          description: `Successfully scraped ${url}${response.data?.cached ? ' (from cache)' : ''}`,
        });
      } else {
        toast({
          title: "Scraping Failed",
          description: response.error || "Unknown error occurred",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error('Scraping error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to scrape website",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const sampleRules = {
    "titles": "h1, h2, h3",
    "links": "a",  
    "images": "img",
    "prices": "\\$[0-9]+\\.?[0-9]*"
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-6">
      <Card className="border-primary/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="w-5 h-5 text-primary" />
            Advanced Web Scraper
          </CardTitle>
          <CardDescription>
            Powerful web scraping service with CORS bypass, caching, and intelligent extraction
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="url">Target URL</Label>
              <Input
                id="url"
                type="url"
                placeholder="https://example.com"
                value={url}
                onChange={(e) => setUrl(e.target.value)}
                className="font-mono"
              />
            </div>
            <div className="space-y-2">
              <Label>Content Type</Label>
              <Tabs value={type} onValueChange={(v) => setType(v as any)}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="html">HTML</TabsTrigger>
                  <TabsTrigger value="json">JSON</TabsTrigger>
                  <TabsTrigger value="text">Text</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="extractRules">
              Extraction Rules (JSON)
              <Badge variant="outline" className="ml-2">Optional</Badge>
            </Label>
            <Textarea
              id="extractRules"
              placeholder={JSON.stringify(sampleRules, null, 2)}
              value={extractRules}
              onChange={(e) => setExtractRules(e.target.value)}
              className="font-mono min-h-[120px]"
            />
            <p className="text-sm text-muted-foreground">
              Define custom extraction rules or leave empty for automatic content extraction
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="cache" className="flex items-center gap-2">
                <Database className="w-4 h-4" />
                Use Cache
              </Label>
              <Switch
                id="cache"
                checked={useCache}
                onCheckedChange={setUseCache}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="timeout" className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Timeout (ms)
              </Label>
              <Input
                id="timeout"
                type="number"
                value={timeout}
                onChange={(e) => setTimeout(Number(e.target.value))}
                min="1000"
                max="30000"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="waitFor">Wait Time (ms)</Label>
              <Input
                id="waitFor"
                type="number"
                value={waitFor}
                onChange={(e) => setWaitFor(Number(e.target.value))}
                min="0"
                max="5000"
              />
            </div>
          </div>

          <Button 
            onClick={handleScrape}
            disabled={isLoading || !url.trim()}
            size="lg"
            className="w-full"
          >
            {isLoading ? (
              <>
                <Zap className="w-4 h-4 mr-2 animate-spin" />
                Scraping...
              </>
            ) : (
              <>
                <Download className="w-4 h-4 mr-2" />
                Scrape Website
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {result && (
        <Card className={result.success ? "border-success/50" : "border-destructive/50"}>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {result.success ? (
                <Badge variant="outline" className="text-success border-success">
                  Success
                </Badge>
              ) : (
                <Badge variant="outline" className="text-destructive border-destructive">
                  <AlertCircle className="w-3 h-3 mr-1" />
                  Error
                </Badge>
              )}
              {result.cached && (
                <Badge variant="secondary">
                  <Database className="w-3 h-3 mr-1" />
                  Cached
                </Badge>
              )}
            </CardTitle>
            {result.metadata && (
              <CardDescription>
                <div className="space-y-1">
                  <p><strong>URL:</strong> {result.metadata.url}</p>
                  <p><strong>Status:</strong> {result.metadata.status}</p>
                  {result.metadata.title && (
                    <p><strong>Title:</strong> {result.metadata.title}</p>
                  )}
                  {result.timestamp && (
                    <p><strong>Scraped:</strong> {new Date(result.timestamp).toLocaleString()}</p>
                  )}
                </div>
              </CardDescription>
            )}
          </CardHeader>
          <CardContent>
            {result.success ? (
              <div className="space-y-4">
                <Tabs defaultValue="formatted" className="w-full">
                  <TabsList>
                    <TabsTrigger value="formatted">Formatted</TabsTrigger>
                    <TabsTrigger value="raw">Raw JSON</TabsTrigger>
                  </TabsList>
                  <TabsContent value="formatted" className="mt-4">
                    <div className="bg-muted/30 p-4 rounded-lg space-y-4">
                      {result.data && typeof result.data === 'object' ? (
                        Object.entries(result.data).map(([key, value]) => (
                          <div key={key} className="space-y-2">
                            <h4 className="font-semibold text-primary capitalize">{key}:</h4>
                            <div className="pl-4 border-l-2 border-primary/20">
                              {Array.isArray(value) ? (
                                <ul className="space-y-1">
                                  {value.slice(0, 10).map((item, idx) => (
                                    <li key={idx} className="text-sm">
                                      {typeof item === 'object' ? JSON.stringify(item) : String(item)}
                                    </li>
                                  ))}
                                  {value.length > 10 && (
                                    <li className="text-sm text-muted-foreground">
                                      ... and {value.length - 10} more items
                                    </li>
                                  )}
                                </ul>
                              ) : (
                                <p className="text-sm">{String(value).substring(0, 500)}</p>
                              )}
                            </div>
                          </div>
                        ))
                      ) : (
                        <p className="text-sm">{String(result.data)}</p>
                      )}
                    </div>
                  </TabsContent>
                  <TabsContent value="raw" className="mt-4">
                    <pre className="bg-muted/30 p-4 rounded-lg text-xs overflow-auto max-h-96">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </TabsContent>
                </Tabs>
              </div>
            ) : (
              <div className="bg-destructive/10 p-4 rounded-lg">
                <p className="text-destructive font-medium">{result.error}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      <Card className="border-info/20 bg-info/5">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-info">
            <AlertCircle className="w-5 h-5" />
            Scraper Features
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-semibold">Bypass CORS</h4>
              <p className="text-sm text-muted-foreground">
                Runs server-side to bypass browser CORS restrictions
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold">Smart Caching</h4>
              <p className="text-sm text-muted-foreground">
                Reduces API calls with configurable TTL caching
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold">Error Handling</h4>
              <p className="text-sm text-muted-foreground">
                Automatic retries, timeouts, and fallback mechanisms
              </p>
            </div>
            <div className="space-y-2">
              <h4 className="font-semibold">Custom Extraction</h4>
              <p className="text-sm text-muted-foreground">
                Flexible JSON-based rules for targeted data extraction
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};