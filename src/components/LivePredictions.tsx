import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Target, Activity } from 'lucide-react';
import { Link } from 'react-router-dom';

const LivePredictions: React.FC = () => {
  return (
    <section className="mt-8">
      <Card className="bg-gradient-card hover:shadow-xl hover:scale-[1.02] transition-all duration-300">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Activity className="w-5 h-5 text-primary" />
            <CardTitle className="font-bruno tracking-wide">Live Predictions</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 rounded-lg bg-card/60 hover:bg-card/80 hover:scale-105 transition-all duration-300 cursor-pointer">
              <p className="text-sm text-muted-foreground">Live Matches</p>
              <p className="text-2xl font-semibold text-foreground">12</p>
            </div>
            <div className="p-4 rounded-lg bg-card/60 hover:bg-card/80 hover:scale-105 transition-all duration-300 cursor-pointer">
              <p className="text-sm text-muted-foreground">Avg Confidence</p>
              <p className="text-2xl font-semibold text-foreground">72%</p>
            </div>
            <div className="p-4 rounded-lg bg-card/60 hover:bg-card/80 hover:scale-105 transition-all duration-300 cursor-pointer">
              <p className="text-sm text-muted-foreground">High-Confidence</p>
              <p className="text-2xl font-semibold text-foreground">5</p>
            </div>
          </div>

          <div className="mt-6 flex justify-center">
            <Button size="lg" asChild className="font-bruno">
              <Link to="/predictions">
                <Target className="w-4 h-4 mr-2" />
                View Live Predictions
              </Link>
            </Button>
          </div>
        </CardContent>
      </Card>
    </section>
  );
};

export default LivePredictions;
