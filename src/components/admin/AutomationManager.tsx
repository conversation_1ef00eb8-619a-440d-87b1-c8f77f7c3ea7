import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { apiService } from '@/services/api';
import { 
  Play, 
  Square, 
  Settings, 
  Activity, 
  BarChart3, 
  TestTube,
  RefreshCw,
  Clock,
  Database,
  Zap
} from 'lucide-react';

interface AutomationStatus {
  scheduler: {
    isRunning: boolean;
    taskCount: number;
  };
  pipeline: {
    recentJobs: number;
    recentPredictions: number;
    lastExecution?: string;
    pythonServiceEnabled: boolean;
  };
}

interface AutomationMetrics {
  totalJobs: number;
  successfulJobs: number;
  failedJobs: number;
  successRate: number;
  totalMatches: number;
  totalPredictions: number;
  pipelineExecutions: number;
}

const AutomationManager: React.FC = () => {
  const [status, setStatus] = useState<AutomationStatus | null>(null);
  const [metrics, setMetrics] = useState<AutomationMetrics | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [config, setConfig] = useState({
    enabled: true,
    sports: ['football', 'basketball', 'baseball'],
    leagues: ['nfl', 'nba', 'mlb'],
    interval: '0 */6 * * *',
    providers: ['espn', 'odds-api'],
    autoPredictions: true,
    maxConcurrent: 3,
  });
  const { toast } = useToast();

  useEffect(() => {
    loadStatus();
    loadMetrics();
    
    // Refresh status every 30 seconds
    const interval = setInterval(() => {
      loadStatus();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const loadStatus = async () => {
    try {
      const response = await apiService.get('/automation/status');
      if (response.success) {
        setStatus(response.data);
      }
    } catch (error) {
      console.error('Failed to load automation status:', error);
    }
  };

  const loadMetrics = async () => {
    try {
      const response = await apiService.get('/automation/metrics?timeframe=24h');
      if (response.success) {
        setMetrics(response.data.metrics);
      }
    } catch (error) {
      console.error('Failed to load automation metrics:', error);
    }
  };

  const startAutomation = async () => {
    setIsLoading(true);
    try {
      const response = await apiService.post('/automation/start', config);
      if (response.success) {
        toast({
          title: "Success",
          description: "Automation system started successfully",
        });
        await loadStatus();
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to start automation system",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const stopAutomation = async () => {
    setIsLoading(true);
    try {
      const response = await apiService.post('/automation/stop');
      if (response.success) {
        toast({
          title: "Success",
          description: "Automation system stopped successfully",
        });
        await loadStatus();
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to stop automation system",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const executePipeline = async () => {
    setIsLoading(true);
    try {
      const response = await apiService.post('/automation/execute-pipeline', {
        sport: 'football',
        league: 'nfl',
      });
      if (response.success) {
        toast({
          title: "Success",
          description: "Pipeline execution started",
        });
        setTimeout(loadMetrics, 5000); // Refresh metrics after 5 seconds
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to execute pipeline",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const testSystem = async () => {
    setIsLoading(true);
    try {
      const response = await apiService.post('/automation/test', { component: 'all' });
      if (response.success) {
        const { overall } = response.data;
        toast({
          title: overall.status === 'passed' ? "Success" : "Warning",
          description: `System test completed: ${overall.passedTests}/${overall.totalTests} tests passed`,
          variant: overall.status === 'passed' ? "default" : "destructive",
        });
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to test automation system",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const updateConfig = async () => {
    setIsLoading(true);
    try {
      const response = await apiService.put('/automation/config', config);
      if (response.success) {
        toast({
          title: "Success",
          description: "Configuration updated successfully",
        });
        await loadStatus();
      } else {
        throw new Error(response.error);
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update configuration",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6" data-aos="fade-up">
      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Scheduler Status</p>
                <div className="flex items-center gap-2 mt-1">
                  <Badge variant={status?.scheduler.isRunning ? "default" : "secondary"}>
                    {status?.scheduler.isRunning ? "Running" : "Stopped"}
                  </Badge>
                  <Activity className={`h-4 w-4 ${status?.scheduler.isRunning ? 'text-green-500' : 'text-gray-400'}`} />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Recent Jobs</p>
                <p className="text-2xl font-bold">{status?.pipeline.recentJobs || 0}</p>
              </div>
              <Database className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Predictions Generated</p>
                <p className="text-2xl font-bold">{status?.pipeline.recentPredictions || 0}</p>
              </div>
              <Zap className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                <p className="text-2xl font-bold">{metrics?.successRate?.toFixed(1) || 0}%</p>
              </div>
              <BarChart3 className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Automation Control Panel
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <Button
              onClick={status?.scheduler.isRunning ? stopAutomation : startAutomation}
              disabled={isLoading}
              variant={status?.scheduler.isRunning ? "destructive" : "default"}
              className="flex items-center gap-2"
            >
              {status?.scheduler.isRunning ? (
                <>
                  <Square className="h-4 w-4" />
                  Stop Automation
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  Start Automation
                </>
              )}
            </Button>

            <Button
              onClick={executePipeline}
              disabled={isLoading}
              variant="outline"
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Execute Pipeline
            </Button>

            <Button
              onClick={testSystem}
              disabled={isLoading}
              variant="outline"
              className="flex items-center gap-2"
            >
              <TestTube className="h-4 w-4" />
              Test System
            </Button>

            <Button
              onClick={loadStatus}
              disabled={isLoading}
              variant="ghost"
              size="sm"
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Configuration and Metrics */}
      <Tabs defaultValue="config" className="space-y-4">
        <TabsList>
          <TabsTrigger value="config">Configuration</TabsTrigger>
          <TabsTrigger value="metrics">Metrics</TabsTrigger>
          <TabsTrigger value="logs">Logs</TabsTrigger>
        </TabsList>

        <TabsContent value="config" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Automation Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="interval">Scraping Interval (Cron)</Label>
                  <Input
                    id="interval"
                    value={config.interval}
                    onChange={(e) => setConfig({ ...config, interval: e.target.value })}
                    placeholder="0 */6 * * *"
                  />
                  <p className="text-xs text-muted-foreground">Every 6 hours by default</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxConcurrent">Max Concurrent Jobs</Label>
                  <Input
                    id="maxConcurrent"
                    type="number"
                    value={config.maxConcurrent}
                    onChange={(e) => setConfig({ ...config, maxConcurrent: parseInt(e.target.value) })}
                    min="1"
                    max="10"
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="autoPredictions"
                  checked={config.autoPredictions}
                  onCheckedChange={(checked) => setConfig({ ...config, autoPredictions: checked })}
                />
                <Label htmlFor="autoPredictions">Enable Automatic Predictions</Label>
              </div>

              <Button onClick={updateConfig} disabled={isLoading}>
                Update Configuration
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="metrics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-muted-foreground">Total Jobs (24h)</p>
                  <p className="text-3xl font-bold">{metrics?.totalJobs || 0}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-muted-foreground">Matches Processed</p>
                  <p className="text-3xl font-bold">{metrics?.totalMatches || 0}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="text-center">
                  <p className="text-sm font-medium text-muted-foreground">Predictions Generated</p>
                  <p className="text-3xl font-bold">{metrics?.totalPredictions || 0}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Logs</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                Automation logs will be displayed here. Check the backend logs for detailed information.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default AutomationManager;
