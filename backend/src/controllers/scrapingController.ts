import { Request, Response, NextFunction } from 'express';
import { ScrapingManager } from '@/services/scraping/scrapingManager';
import { createError } from '@/middleware/errorHandler';
import { logger } from '@/utils/logger';
import { AuthenticatedRequest } from '@/middleware/auth';

const scrapingManager = new ScrapingManager();

export const scrapeProvider = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const { provider, sport, league } = req.body;

    if (!provider || !sport) {
      return next(createError('Provider and sport are required', 400));
    }

    const supportedProviders = scrapingManager.getSupportedProviders();
    if (!supportedProviders.includes(provider.toLowerCase())) {
      return next(createError(`Unsupported provider. Supported: ${supportedProviders.join(', ')}`, 400));
    }

    const supportedSports = scrapingManager.getSupportedSports();
    if (!supportedSports.includes(sport.toLowerCase())) {
      return next(createError(`Unsupported sport. Supported: ${supportedSports.join(', ')}`, 400));
    }

    logger.info(`Starting scraping job for ${provider}/${sport}/${league} by user ${req.user?.email}`);

    const result = await scrapingManager.scrapeProvider(provider, sport, league);

    res.json({
      success: true,
      message: `Scraping completed for ${provider}`,
      data: {
        provider: result.source,
        matchesFound: result.matchesFound,
        success: result.success,
        error: result.error,
        timestamp: result.timestamp,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const scrapeAllProviders = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const { sport, league } = req.body;

    if (!sport) {
      return next(createError('Sport is required', 400));
    }

    const supportedSports = scrapingManager.getSupportedSports();
    if (!supportedSports.includes(sport.toLowerCase())) {
      return next(createError(`Unsupported sport. Supported: ${supportedSports.join(', ')}`, 400));
    }

    logger.info(`Starting comprehensive scraping job for ${sport}/${league} by user ${req.user?.email}`);

    // Start scraping job asynchronously
    const jobPromise = scrapingManager.scrapeAllProviders(sport, league);

    // Return immediately with job info
    res.json({
      success: true,
      message: 'Scraping job started',
      data: {
        sport,
        league,
        status: 'started',
        timestamp: new Date(),
      },
    });

    // Continue processing in background
    jobPromise.then(result => {
      logger.info(`Scraping job completed: ${result.jobId}, saved ${result.savedMatches}/${result.totalMatches} matches`);
    }).catch(error => {
      logger.error(`Scraping job failed: ${error}`);
    });

  } catch (error) {
    next(error);
  }
};

export const getScrapingJob = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { jobId } = req.params;

    const job = await scrapingManager.getScrapingJob(jobId);

    if (!job) {
      return next(createError('Scraping job not found', 404));
    }

    res.json({
      success: true,
      data: { job },
    });
  } catch (error) {
    next(error);
  }
};

export const getRecentScrapingJobs = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { limit = 10 } = req.query;

    const jobs = await scrapingManager.getRecentScrapingJobs(Number(limit));

    res.json({
      success: true,
      data: { jobs },
    });
  } catch (error) {
    next(error);
  }
};

export const getSupportedProviders = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const providers = scrapingManager.getSupportedProviders();
    const sports = scrapingManager.getSupportedSports();

    const sportsWithLeagues = sports.reduce((acc, sport) => {
      acc[sport] = scrapingManager.getSupportedLeagues(sport);
      return acc;
    }, {} as { [key: string]: string[] });

    res.json({
      success: true,
      data: {
        providers,
        sports: sportsWithLeagues,
      },
    });
  } catch (error) {
    next(error);
  }
};

export const scheduledScraping = async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
  try {
    const { sports, enabled = true } = req.body;

    if (!Array.isArray(sports) || sports.length === 0) {
      return next(createError('Sports array is required', 400));
    }

    const supportedSports = scrapingManager.getSupportedSports();
    const invalidSports = sports.filter(sport => !supportedSports.includes(sport.toLowerCase()));

    if (invalidSports.length > 0) {
      return next(createError(`Unsupported sports: ${invalidSports.join(', ')}`, 400));
    }

    // In a real implementation, this would set up cron jobs or scheduled tasks
    logger.info(`Scheduled scraping ${enabled ? 'enabled' : 'disabled'} for sports: ${sports.join(', ')}`);

    res.json({
      success: true,
      message: `Scheduled scraping ${enabled ? 'enabled' : 'disabled'}`,
      data: {
        sports,
        enabled,
        schedule: '0 */6 * * *', // Every 6 hours
      },
    });
  } catch (error) {
    next(error);
  }
};
