# PowerShell deployment script for scrape-and-predict edge function
param(
    [string]$Environment = "production",
    [switch]$Force,
    [switch]$Test
)

Write-Host "🚀 Deploying scrape-and-predict edge function..." -ForegroundColor Green

# Check if Supabase CLI is installed
try {
    $supabaseVersion = supabase --version
    Write-Host "✅ Supabase CLI found: $supabaseVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Supabase CLI not found. Please install it first:" -ForegroundColor Red
    Write-Host "npm install -g supabase" -ForegroundColor Yellow
    exit 1
}

# Check if we're in the right directory
if (-not (Test-Path "supabase\functions\scrape-and-predict\index.ts")) {
    Write-Host "❌ Not in the correct project directory. Please run from project root." -ForegroundColor Red
    exit 1
}

# Login check
Write-Host "🔐 Checking Supabase authentication..." -ForegroundColor Blue
try {
    $loginStatus = supabase projects list 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-Host "❌ Not logged in to Supabase. Please run: supabase login" -ForegroundColor Red
        exit 1
    }
    Write-Host "✅ Authenticated with Supabase" -ForegroundColor Green
} catch {
    Write-Host "❌ Authentication check failed" -ForegroundColor Red
    exit 1
}

# Deploy the function
Write-Host "📦 Deploying edge function..." -ForegroundColor Blue
try {
    if ($Force) {
        $deployResult = supabase functions deploy scrape-and-predict --no-verify-jwt
    } else {
        $deployResult = supabase functions deploy scrape-and-predict
    }
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Edge function deployed successfully!" -ForegroundColor Green
    } else {
        Write-Host "❌ Deployment failed" -ForegroundColor Red
        exit 1
    }
} catch {
    Write-Host "❌ Deployment error: $_" -ForegroundColor Red
    exit 1
}

# Set environment variables
Write-Host "🔧 Setting environment variables..." -ForegroundColor Blue
$envVars = @(
    "SUPABASE_URL",
    "SUPABASE_SERVICE_ROLE_KEY",
    "PY_SERVICE_URL"
)

foreach ($var in $envVars) {
    $value = [Environment]::GetEnvironmentVariable($var)
    if ($value) {
        try {
            supabase secrets set $var="$value"
            Write-Host "✅ Set $var" -ForegroundColor Green
        } catch {
            Write-Host "⚠️  Failed to set $var" -ForegroundColor Yellow
        }
    } else {
        Write-Host "⚠️  Environment variable $var not found locally" -ForegroundColor Yellow
    }
}

# Test the function if requested
if ($Test) {
    Write-Host "🧪 Testing the deployed function..." -ForegroundColor Blue
    
    # Get project reference
    $projectRef = (supabase status | Select-String "API URL:" | ForEach-Object { $_.ToString().Split(": ")[1] }).Replace("http://127.0.0.1:54321", "https://bylrsugfuksgjbhpjgxh.supabase.co")
    
    $testPayload = @{
        sport = "football"
        league = "NFL"
        auto_predict = $true
    } | ConvertTo-Json
    
    try {
        $response = Invoke-RestMethod -Uri "$projectRef/functions/v1/scrape-and-predict" -Method POST -Body $testPayload -ContentType "application/json" -Headers @{
            "Authorization" = "Bearer YOUR_TEST_TOKEN_HERE"
        }
        
        Write-Host "✅ Test successful!" -ForegroundColor Green
        Write-Host "Response: $($response | ConvertTo-Json -Depth 3)" -ForegroundColor Cyan
    } catch {
        Write-Host "⚠️  Test failed (this is expected without proper auth token): $_" -ForegroundColor Yellow
    }
}

Write-Host "`n🎉 Deployment completed!" -ForegroundColor Green
Write-Host "Function URL: https://bylrsugfuksgjbhpjgxh.supabase.co/functions/v1/scrape-and-predict" -ForegroundColor Cyan
Write-Host "`nNext steps:" -ForegroundColor Blue
Write-Host "1. Update your environment variables in Supabase dashboard" -ForegroundColor White
Write-Host "2. Test with proper authentication token" -ForegroundColor White
Write-Host "3. Set up monitoring and alerts" -ForegroundColor White
