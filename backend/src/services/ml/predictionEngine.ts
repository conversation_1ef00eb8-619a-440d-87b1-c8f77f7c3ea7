import { FeatureVector, FeatureEngineer } from './featureEngineering';
import { MatchData } from '@/services/scraping/baseScraper';
import { logger } from '@/utils/logger';

export interface PredictionResult {
  market: string;
  pick: string;
  confidence: number;
  probability: number;
  odds: number;
  expectedValue: number;
  edge: number;
  factors: { [key: string]: number };
  analysis: string;
}

export interface ModelPrediction {
  homeWinProbability: number;
  awayWinProbability: number;
  drawProbability?: number;
  spread: number;
  total: number;
  confidence: number;
}

export class PredictionEngine {
  private featureEngineer: FeatureEngineer;
  private models: Map<string, any>;

  constructor() {
    this.featureEngineer = new FeatureEngineer();
    this.models = new Map();
    this.initializeModels();
  }

  private initializeModels(): void {
    // Initialize different models for different sports and markets
    this.models.set('moneyline', new MoneylineModel());
    this.models.set('spread', new SpreadModel());
    this.models.set('total', new TotalModel());
  }

  async generatePredictions(match: MatchData, markets: string[] = ['moneyline', 'spread', 'total']): Promise<PredictionResult[]> {
    try {
      logger.info(`Generating predictions for ${match.homeTeam} vs ${match.awayTeam}`);

      // Extract features
      const features = await this.featureEngineer.extractFeatures(match);
      
      // Generate base model prediction
      const modelPrediction = await this.generateModelPrediction(features, match);
      
      const predictions: PredictionResult[] = [];

      for (const market of markets) {
        try {
          const prediction = await this.generateMarketPrediction(
            market,
            modelPrediction,
            features,
            match
          );
          
          if (prediction && this.isValidPrediction(prediction)) {
            predictions.push(prediction);
          }
        } catch (error) {
          logger.error(`Failed to generate ${market} prediction: ${error}`);
        }
      }

      logger.info(`Generated ${predictions.length} predictions for ${match.homeTeam} vs ${match.awayTeam}`);
      return predictions;

    } catch (error) {
      logger.error(`Prediction generation failed: ${error}`);
      return [];
    }
  }

  private async generateModelPrediction(features: FeatureVector, match: MatchData): Promise<ModelPrediction> {
    // Ensemble approach combining multiple algorithms
    const predictions = await Promise.all([
      this.logisticRegressionPrediction(features),
      this.randomForestPrediction(features),
      this.neuralNetworkPrediction(features),
      this.xgboostPrediction(features),
    ]);

    // Weighted ensemble
    const weights = [0.25, 0.30, 0.25, 0.20]; // Adjust based on model performance
    
    let homeWinProb = 0;
    let awayWinProb = 0;
    let spread = 0;
    let total = 0;
    let confidence = 0;

    for (let i = 0; i < predictions.length; i++) {
      homeWinProb += predictions[i].homeWinProbability * weights[i];
      awayWinProb += predictions[i].awayWinProbability * weights[i];
      spread += predictions[i].spread * weights[i];
      total += predictions[i].total * weights[i];
      confidence += predictions[i].confidence * weights[i];
    }

    return {
      homeWinProbability: homeWinProb,
      awayWinProbability: awayWinProb,
      spread,
      total,
      confidence,
    };
  }

  private async generateMarketPrediction(
    market: string,
    modelPrediction: ModelPrediction,
    features: FeatureVector,
    match: MatchData
  ): Promise<PredictionResult | null> {
    switch (market.toLowerCase()) {
      case 'moneyline':
        return this.generateMoneylinePrediction(modelPrediction, features, match);
      case 'spread':
        return this.generateSpreadPrediction(modelPrediction, features, match);
      case 'total':
        return this.generateTotalPrediction(modelPrediction, features, match);
      default:
        logger.warn(`Unsupported market: ${market}`);
        return null;
    }
  }

  private generateMoneylinePrediction(
    modelPrediction: ModelPrediction,
    features: FeatureVector,
    match: MatchData
  ): PredictionResult {
    const homeProb = modelPrediction.homeWinProbability;
    const awayProb = modelPrediction.awayWinProbability;
    
    const pick = homeProb > awayProb ? match.homeTeam : match.awayTeam;
    const probability = Math.max(homeProb, awayProb);
    
    // Get market odds
    const marketOdds = match.odds?.moneyline;
    const odds = homeProb > awayProb ? 
      (marketOdds?.home || this.probabilityToAmericanOdds(homeProb)) :
      (marketOdds?.away || this.probabilityToAmericanOdds(awayProb));

    const expectedValue = this.calculateExpectedValue(probability, odds);
    const edge = this.calculateEdge(probability, odds);

    const factors = this.calculateMoneylineFactors(features);
    const analysis = this.generateMoneylineAnalysis(pick, probability, factors, match);

    return {
      market: 'Moneyline',
      pick,
      confidence: modelPrediction.confidence,
      probability,
      odds,
      expectedValue,
      edge,
      factors,
      analysis,
    };
  }

  private generateSpreadPrediction(
    modelPrediction: ModelPrediction,
    features: FeatureVector,
    match: MatchData
  ): PredictionResult {
    const predictedSpread = modelPrediction.spread;
    const marketSpread = match.odds?.spread?.line || 0;
    
    const spreadDiff = Math.abs(predictedSpread - marketSpread);
    const confidence = Math.min(spreadDiff * 10, 95); // Higher diff = higher confidence
    
    const pick = predictedSpread > marketSpread ? 
      `${match.awayTeam} +${Math.abs(marketSpread)}` :
      `${match.homeTeam} ${marketSpread >= 0 ? '+' : ''}${marketSpread}`;

    const probability = this.spreadDiffToProbability(spreadDiff);
    const odds = match.odds?.spread?.home || -110;
    
    const expectedValue = this.calculateExpectedValue(probability, odds);
    const edge = this.calculateEdge(probability, odds);

    const factors = this.calculateSpreadFactors(features, predictedSpread, marketSpread);
    const analysis = this.generateSpreadAnalysis(pick, predictedSpread, marketSpread, factors, match);

    return {
      market: 'Spread',
      pick,
      confidence,
      probability,
      odds,
      expectedValue,
      edge,
      factors,
      analysis,
    };
  }

  private generateTotalPrediction(
    modelPrediction: ModelPrediction,
    features: FeatureVector,
    match: MatchData
  ): PredictionResult {
    const predictedTotal = modelPrediction.total;
    const marketTotal = match.odds?.total?.line || 0;
    
    const totalDiff = Math.abs(predictedTotal - marketTotal);
    const confidence = Math.min(totalDiff * 5, 90);
    
    const pick = predictedTotal > marketTotal ? 
      `Over ${marketTotal}` : 
      `Under ${marketTotal}`;

    const probability = this.totalDiffToProbability(totalDiff);
    const odds = predictedTotal > marketTotal ? 
      (match.odds?.total?.over || -110) :
      (match.odds?.total?.under || -110);
    
    const expectedValue = this.calculateExpectedValue(probability, odds);
    const edge = this.calculateEdge(probability, odds);

    const factors = this.calculateTotalFactors(features, predictedTotal, marketTotal);
    const analysis = this.generateTotalAnalysis(pick, predictedTotal, marketTotal, factors, match);

    return {
      market: 'Total',
      pick,
      confidence,
      probability,
      odds,
      expectedValue,
      edge,
      factors,
      analysis,
    };
  }

  // Simplified model implementations (in production, these would be trained ML models)
  private async logisticRegressionPrediction(features: FeatureVector): Promise<ModelPrediction> {
    const ratingDiff = features.home_rating - features.away_rating + features.home_field_advantage;
    const formDiff = (features.home_recent_wins - features.home_recent_losses) - 
                    (features.away_recent_wins - features.away_recent_losses);
    
    const homeAdvantage = ratingDiff + formDiff * 0.5;
    const homeWinProb = this.sigmoid(homeAdvantage / 10);
    
    return {
      homeWinProbability: homeWinProb,
      awayWinProbability: 1 - homeWinProb,
      spread: homeAdvantage * 0.8,
      total: (features.home_recent_avg_score + features.away_recent_avg_score) * 1.1 || 45,
      confidence: 75,
    };
  }

  private async randomForestPrediction(features: FeatureVector): Promise<ModelPrediction> {
    // Simplified random forest simulation
    const trees = 100;
    let homeWins = 0;
    
    for (let i = 0; i < trees; i++) {
      const randomFactor = (Math.random() - 0.5) * 10;
      const score = features.home_rating - features.away_rating + 
                   features.home_field_advantage + randomFactor;
      if (score > 0) homeWins++;
    }
    
    const homeWinProb = homeWins / trees;
    
    return {
      homeWinProbability: homeWinProb,
      awayWinProbability: 1 - homeWinProb,
      spread: (features.home_rating - features.away_rating + features.home_field_advantage) * 0.7,
      total: features.home_recent_avg_score + features.away_recent_avg_score + 5,
      confidence: 80,
    };
  }

  private async neuralNetworkPrediction(features: FeatureVector): Promise<ModelPrediction> {
    // Simplified neural network simulation
    const inputs = [
      features.home_rating / 100,
      features.away_rating / 100,
      features.home_field_advantage / 10,
      features.h2h_home_wins / (features.h2h_total_games || 1),
      features.home_recent_wins / 10,
      features.away_recent_wins / 10,
    ];
    
    // Simple forward pass simulation
    let output = 0;
    for (let i = 0; i < inputs.length; i++) {
      output += inputs[i] * (0.5 + Math.random() * 0.5); // Random weights
    }
    
    const homeWinProb = this.sigmoid(output);
    
    return {
      homeWinProbability: homeWinProb,
      awayWinProbability: 1 - homeWinProb,
      spread: (output - 0.5) * 20,
      total: 45 + output * 20,
      confidence: 85,
    };
  }

  private async xgboostPrediction(features: FeatureVector): Promise<ModelPrediction> {
    // Simplified XGBoost simulation
    const featureImportance = {
      rating_diff: 0.25,
      home_field: 0.15,
      recent_form: 0.20,
      h2h: 0.15,
      rest: 0.10,
      injuries: 0.15,
    };
    
    const ratingDiff = features.home_rating - features.away_rating;
    const formDiff = features.home_recent_wins - features.away_recent_wins;
    const h2hAdvantage = features.h2h_home_wins - features.h2h_away_wins;
    
    const score = ratingDiff * featureImportance.rating_diff +
                  features.home_field_advantage * featureImportance.home_field +
                  formDiff * featureImportance.recent_form +
                  h2hAdvantage * featureImportance.h2h;
    
    const homeWinProb = this.sigmoid(score / 15);
    
    return {
      homeWinProbability: homeWinProb,
      awayWinProbability: 1 - homeWinProb,
      spread: score * 0.6,
      total: features.home_recent_avg_score + features.away_recent_avg_score + 8,
      confidence: 90,
    };
  }

  // Utility functions
  private sigmoid(x: number): number {
    return 1 / (1 + Math.exp(-x));
  }

  private probabilityToAmericanOdds(probability: number): number {
    if (probability >= 0.5) {
      return Math.round(-100 * probability / (1 - probability));
    } else {
      return Math.round(100 * (1 - probability) / probability);
    }
  }

  private calculateExpectedValue(probability: number, americanOdds: number): number {
    const decimalOdds = americanOdds > 0 ? 
      (americanOdds / 100) + 1 : 
      (100 / Math.abs(americanOdds)) + 1;
    
    return (probability * decimalOdds) - 1;
  }

  private calculateEdge(probability: number, americanOdds: number): number {
    const impliedProbability = americanOdds > 0 ? 
      100 / (americanOdds + 100) : 
      Math.abs(americanOdds) / (Math.abs(americanOdds) + 100);
    
    return probability - impliedProbability;
  }

  private spreadDiffToProbability(diff: number): number {
    return Math.min(0.5 + (diff * 0.05), 0.95);
  }

  private totalDiffToProbability(diff: number): number {
    return Math.min(0.5 + (diff * 0.03), 0.90);
  }

  private calculateMoneylineFactors(features: FeatureVector): { [key: string]: number } {
    return {
      team_strength: (features.home_rating - features.away_rating) / 100,
      home_field_advantage: features.home_field_advantage / 10,
      recent_form: (features.home_recent_wins - features.away_recent_wins) / 10,
      head_to_head: features.h2h_total_games > 0 ? 
        (features.h2h_home_wins - features.h2h_away_wins) / features.h2h_total_games : 0,
      rest_advantage: (features.rest_days_home - features.rest_days_away) / 7,
    };
  }

  private calculateSpreadFactors(features: FeatureVector, predicted: number, market: number): { [key: string]: number } {
    return {
      model_edge: (predicted - market) / 10,
      team_strength: (features.home_rating - features.away_rating) / 100,
      offensive_advantage: (features.home_offense_rating - features.away_defense_rating) / 100,
      defensive_advantage: (features.away_offense_rating - features.home_defense_rating) / 100,
      situational: features.home_field_advantage / 10,
    };
  }

  private calculateTotalFactors(features: FeatureVector, predicted: number, market: number): { [key: string]: number } {
    return {
      model_edge: (predicted - market) / 20,
      offensive_pace: (features.home_offense_rating + features.away_offense_rating) / 200,
      defensive_strength: (features.home_defense_rating + features.away_defense_rating) / 200,
      recent_scoring: (features.home_recent_avg_score + features.away_recent_avg_score) / 100,
      weather_impact: 0, // Would need weather data
    };
  }

  private generateMoneylineAnalysis(pick: string, probability: number, factors: any, match: MatchData): string {
    const confidence = probability > 0.6 ? 'High' : probability > 0.55 ? 'Medium' : 'Low';
    const keyFactor = Object.entries(factors).reduce((a, b) => Math.abs(a[1] as number) > Math.abs(b[1] as number) ? a : b);

    return `${confidence} confidence pick on ${pick}. Key factor: ${keyFactor[0]} (${(keyFactor[1] as number) > 0 ? '+' : ''}${((keyFactor[1] as number) * 100).toFixed(1)}%). Model probability: ${(probability * 100).toFixed(1)}%.`;
  }

  private generateSpreadAnalysis(pick: string, predicted: number, market: number, factors: any, match: MatchData): string {
    const edge = predicted - market;
    const direction = edge > 0 ? 'favors away team' : 'favors home team';
    
    return `Model predicts ${predicted.toFixed(1)} point spread vs market ${market}. Edge of ${Math.abs(edge).toFixed(1)} points ${direction}. Recommendation: ${pick}.`;
  }

  private generateTotalAnalysis(pick: string, predicted: number, market: number, factors: any, match: MatchData): string {
    const edge = predicted - market;
    const direction = edge > 0 ? 'higher scoring' : 'lower scoring';
    
    return `Model predicts ${predicted.toFixed(1)} total points vs market ${market}. Expecting ${direction} game with ${Math.abs(edge).toFixed(1)} point edge. Recommendation: ${pick}.`;
  }

  private isValidPrediction(prediction: PredictionResult): boolean {
    return prediction.confidence >= (parseFloat(process.env.PREDICTION_CONFIDENCE_THRESHOLD || '65')) &&
           Math.abs(prediction.edge) >= 0.02 && // At least 2% edge
           prediction.expectedValue > 0; // Positive expected value
  }
}

// Placeholder model classes (would be actual trained models in production)
class MoneylineModel {
  predict(features: FeatureVector): number {
    return Math.random(); // Placeholder
  }
}

class SpreadModel {
  predict(features: FeatureVector): number {
    return Math.random() * 20 - 10; // Placeholder
  }
}

class TotalModel {
  predict(features: FeatureVector): number {
    return Math.random() * 100 + 150; // Placeholder
  }
}
