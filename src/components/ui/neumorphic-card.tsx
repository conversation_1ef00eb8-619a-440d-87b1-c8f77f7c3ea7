import React from 'react';
import { cn } from '@/lib/utils';

interface NeumorphicCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'inset' | 'button';
  hover?: boolean;
  pulse?: boolean;
}

const NeumorphicCard = React.forwardRef<HTMLDivElement, NeumorphicCardProps>(
  ({ className, variant = 'default', hover = true, pulse = false, children, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'neumorphic rounded-lg p-4',
          variant === 'inset' && 'neumorphic-inset',
          variant === 'button' && 'cursor-pointer active:scale-95',
          hover && 'micro-bounce',
          pulse && 'pulse-slow',
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }
);

NeumorphicCard.displayName = 'NeumorphicCard';

export { NeumorphicCard };