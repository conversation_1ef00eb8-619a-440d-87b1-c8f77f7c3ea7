import React, { useState } from 'react';
import { Header } from '@/components/Header';
import { GlobalSettingsManager } from '@/components/admin/GlobalSettingsManager';
import { DataScrapingManager } from '@/components/admin/DataScrapingManager';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useIsMobile } from '@/hooks/use-mobile';
import { 
  Settings, 
  Server, 
  Database, 
  Shield, 
  Bell, 
  Globe, 
  Palette,
  Mail,
  Users,
  CreditCard,
  BarChart3
} from 'lucide-react';

const GlobalAdminSettings: React.FC = () => {
  const isMobile = useIsMobile();
  const [settings, setSettings] = useState({
    siteName: '1300BLK AI',
    siteDescription: 'Next-generation AI sports predictions',
    maintenanceMode: false,
    allowRegistration: true,
    emailNotifications: true,
    pushNotifications: false,
    maxUsersPerIp: 5,
    sessionTimeout: 30
  });

  const handleSettingChange = (key: string, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <Tabs defaultValue="global-settings" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="global-settings">Global Settings</TabsTrigger>
            <TabsTrigger value="data-scraping">Data Scraping</TabsTrigger>
          </TabsList>

          <TabsContent value="global-settings" className="mt-6">
            <GlobalSettingsManager />
          </TabsContent>

          <TabsContent value="data-scraping" className="mt-6">
            <DataScrapingManager />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default GlobalAdminSettings;