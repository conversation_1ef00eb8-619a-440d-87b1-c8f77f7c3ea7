import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 shadow-[6px_6px_12px_rgba(0,0,0,0.25),_-6px_-6px_12px_rgba(255,255,255,0.06)] hover:shadow-[4px_4px_8px_rgba(0,0,0,0.25),_-4px_-4px_8px_rgba(255,255,255,0.06)]",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground border border-white hover:bg-primary/80 active:shadow-[inset_6px_6px_12px_rgba(0,0,0,0.2),_inset_-6px_-6px_12px_rgba(255,255,255,0.05)]",
        destructive:
          "bg-primary text-primary-foreground border border-white hover:bg-primary/80",
        outline:
          "border-2 border-white border-dotted bg-background text-foreground hover:bg-primary/20 active:shadow-[inset_6px_6px_12px_rgba(0,0,0,0.2),_inset_-6px_-6px_12px_rgba(255,255,255,0.05)]",
        secondary:
          "bg-primary text-primary-foreground border border-white hover:bg-primary/80",
        ghost: "text-foreground hover:bg-primary/20 border border-dotted border-white",
        link: "text-primary underline-offset-4 hover:underline border-none",
      },
      size: {
        default: "h-10 px-4 py-2 sm:h-8 sm:px-3 sm:py-1.5",
        sm: "h-8 rounded-md px-2.5 sm:h-7 sm:px-2",
        lg: "h-11 rounded-md px-8 sm:h-9 sm:px-6",
        icon: "h-10 w-10 sm:h-8 sm:w-8",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
