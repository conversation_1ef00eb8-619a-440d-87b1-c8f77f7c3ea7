export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          role: 'user' | 'admin' | 'super_admin'
          subscription_tier: 'free' | 'basic' | 'premium' | 'enterprise' | null
          subscription_status: 'active' | 'canceled' | 'past_due' | null
          subscription_end: string | null
          stripe_customer_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'user' | 'admin' | 'super_admin'
          subscription_tier?: 'free' | 'basic' | 'premium' | 'enterprise' | null
          subscription_status?: 'active' | 'canceled' | 'past_due' | null
          subscription_end?: string | null
          stripe_customer_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'user' | 'admin' | 'super_admin'
          subscription_tier?: 'free' | 'basic' | 'premium' | 'enterprise' | null
          subscription_status?: 'active' | 'canceled' | 'past_due' | null
          subscription_end?: string | null
          stripe_customer_id?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      predictions: {
        Row: {
          id: string
          sport: string
          league: string
          home_team: string
          away_team: string
          match_date: string
          market: string
          pick: string
          confidence: number
          odds: number
          edge: number
          expected_value: number | null
          source: 'ML' | 'MANUAL' | 'SCRAPED'
          status: 'upcoming' | 'won' | 'lost' | 'void'
          result: string | null
          factors: any | null
          created_by: string | null
          is_premium: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          sport: string
          league: string
          home_team: string
          away_team: string
          match_date: string
          market: string
          pick: string
          confidence: number
          odds: number
          edge: number
          expected_value?: number | null
          source?: 'ML' | 'MANUAL' | 'SCRAPED'
          status?: 'upcoming' | 'won' | 'lost' | 'void'
          result?: string | null
          factors?: any | null
          created_by?: string | null
          is_premium?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          sport?: string
          league?: string
          home_team?: string
          away_team?: string
          match_date?: string
          market?: string
          pick?: string
          confidence?: number
          odds?: number
          edge?: number
          expected_value?: number | null
          source?: 'ML' | 'MANUAL' | 'SCRAPED'
          status?: 'upcoming' | 'won' | 'lost' | 'void'
          result?: string | null
          factors?: any | null
          created_by?: string | null
          is_premium?: boolean
          created_at?: string
          updated_at?: string
        }
      }
      sports_data: {
        Row: {
          id: string
          sport: string
          league: string
          match_id: string
          home_team: string
          away_team: string
          match_date: string
          odds: any | null
          stats: any | null
          source: string
          scraped_at: string
          created_at: string
        }
        Insert: {
          id?: string
          sport: string
          league: string
          match_id: string
          home_team: string
          away_team: string
          match_date: string
          odds?: any | null
          stats?: any | null
          source: string
          scraped_at?: string
          created_at?: string
        }
        Update: {
          id?: string
          sport?: string
          league?: string
          match_id?: string
          home_team?: string
          away_team?: string
          match_date?: string
          odds?: any | null
          stats?: any | null
          source?: string
          scraped_at?: string
          created_at?: string
        }
      }
      global_settings: {
        Row: {
          id: string
          key: string
          value: any
          description: string | null
          updated_by: string | null
          updated_at: string
          created_at: string
        }
        Insert: {
          id?: string
          key: string
          value: any
          description?: string | null
          updated_by?: string | null
          updated_at?: string
          created_at?: string
        }
        Update: {
          id?: string
          key?: string
          value?: any
          description?: string | null
          updated_by?: string | null
          updated_at?: string
          created_at?: string
        }
      }
      audit_logs: {
        Row: {
          id: string
          user_id: string | null
          action: string
          resource_type: string
          resource_id: string | null
          details: any | null
          ip_address: string | null
          user_agent: string | null
          created_at: string
        }
        Insert: {
          id?: string
          user_id?: string | null
          action: string
          resource_type: string
          resource_id?: string | null
          details?: any | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          action?: string
          resource_type?: string
          resource_id?: string | null
          details?: any | null
          ip_address?: string | null
          user_agent?: string | null
          created_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}