
import React from 'react';
import { Helmet } from 'react-helmet-async';
import { generateSEOMetadata, SEOMetadata } from '@/utils/seo';
import { OG_IMAGES, getSportOGImage } from '@/utils/ogImageUrls';

interface SEOHeadProps extends SEOMetadata {
  sport?: string;
  breadcrumbs?: Array<{ name: string; url: string }>;
  structuredData?: object[];
}

export const SEOHead: React.FC<SEOHeadProps> = ({
  title,
  description,
  keywords,
  canonical,
  ogImage,
  ogType = 'website',
  sport,
  breadcrumbs,
  structuredData = []
}) => {
  // Don't render if we don't have the minimum required props
  if (!title || !description) {
    return null;
  }

  const seoData = generateSEOMetadata({
    title,
    description,
    keywords,
    canonical,
    ogImage: ogImage || (sport ? getSportOGImage(sport) : OG_IMAGES.home),
    ogType
  });

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{seoData.title}</title>
      <meta name="description" content={seoData.description} />
      {seoData.keywords && <meta name="keywords" content={seoData.keywords} />}
      {seoData.canonical && <link rel="canonical" href={seoData.canonical} />}
      
      {/* Open Graph */}
      <meta property="og:title" content={seoData.openGraph.title} />
      <meta property="og:description" content={seoData.openGraph.description} />
      <meta property="og:url" content={seoData.openGraph.url} />
      <meta property="og:type" content={seoData.openGraph.type} />
      <meta property="og:image" content={seoData.openGraph.images[0].url} />
      <meta property="og:image:width" content={seoData.openGraph.images[0].width.toString()} />
      <meta property="og:image:height" content={seoData.openGraph.images[0].height.toString()} />
      <meta property="og:image:alt" content={seoData.openGraph.images[0].alt} />
      <meta property="og:site_name" content={seoData.openGraph.siteName} />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content={seoData.twitter.card} />
      <meta name="twitter:title" content={seoData.twitter.title} />
      <meta name="twitter:description" content={seoData.twitter.description} />
      <meta name="twitter:image" content={seoData.twitter.images[0]} />
      <meta name="twitter:site" content={seoData.twitter.site} />
      <meta name="twitter:creator" content={seoData.twitter.creator} />
      
      {/* Additional SEO */}
      <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1" />
      <meta name="googlebot" content="index, follow, max-image-preview:large, max-snippet:-1" />
      <meta name="author" content="ML Sports Intelligent Engine" />
      <meta name="theme-color" content="#10b981" />
      
      {/* Structured Data */}
      {structuredData && structuredData.length > 0 && structuredData.map((data, index) => (
        <script key={index} type="application/ld+json">
          {JSON.stringify(data)}
        </script>
      ))}
    </Helmet>
  );
};
