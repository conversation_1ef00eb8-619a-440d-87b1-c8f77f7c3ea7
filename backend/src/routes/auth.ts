import { Router } from 'express';
import { register, login, getProfile, updateProfile, changePassword } from '@/controllers/authController';
import { authenticate } from '@/middleware/auth';
import { validate } from '@/utils/validation';
import { registerSchema, loginSchema, updateUserSchema, changePasswordSchema } from '@/utils/validation';

const router = Router();

// Public routes
router.post('/register', validate(registerSchema), register);
router.post('/login', validate(loginSchema), login);

// Protected routes
router.get('/profile', authenticate, getProfile);
router.put('/profile', authenticate, validate(updateUserSchema), updateProfile);
router.put('/change-password', authenticate, validate(changePasswordSchema), changePassword);

export default router;
