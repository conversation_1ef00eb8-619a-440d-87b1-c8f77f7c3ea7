import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '@/middleware/auth';
import { scrapingScheduler } from '@/services/automation/scrapingScheduler';
import { dataPipeline } from '@/services/automation/dataPipeline';
import { logger } from '@/utils/logger';
import { createError } from '@/middleware/errorHandler';

/**
 * Get automation system status
 */
export const getAutomationStatus = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const schedulerStatus = scrapingScheduler.getStatus();
    const pipelineStatus = await dataPipeline.getStatus();

    res.json({
      success: true,
      data: {
        scheduler: schedulerStatus,
        pipeline: pipelineStatus,
        timestamp: new Date(),
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Start automated scraping and prediction system
 */
export const startAutomation = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { sports, leagues, interval, providers, autoPredictions } = req.body;

    logger.info(`Starting automation system by user ${req.user?.email}`);

    // Initialize the scheduler
    await scrapingScheduler.initialize();

    // Update configuration if provided
    if (sports || leagues || interval || providers || autoPredictions !== undefined) {
      const config: any = {};
      if (sports) config.sports = sports;
      if (leagues) config.leagues = leagues;
      if (interval) config.interval = interval;
      if (providers) config.providers = providers;
      if (autoPredictions !== undefined) config.autoPredictions = autoPredictions;

      await scrapingScheduler.updateConfiguration(config);
    }

    res.json({
      success: true,
      message: 'Automation system started successfully',
      data: {
        status: 'running',
        timestamp: new Date(),
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Stop automated scraping and prediction system
 */
export const stopAutomation = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    logger.info(`Stopping automation system by user ${req.user?.email}`);

    await scrapingScheduler.stopScheduledScraping();

    res.json({
      success: true,
      message: 'Automation system stopped successfully',
      data: {
        status: 'stopped',
        timestamp: new Date(),
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Execute data pipeline manually
 */
export const executePipeline = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { sport, league } = req.body;

    if (!sport) {
      throw createError('Sport is required', 400);
    }

    logger.info(`Manual pipeline execution for ${sport}/${league} by user ${req.user?.email}`);

    // Execute pipeline asynchronously
    const pipelinePromise = dataPipeline.executePipeline(sport, league);

    // Return immediately
    res.json({
      success: true,
      message: 'Pipeline execution started',
      data: {
        sport,
        league,
        status: 'running',
        timestamp: new Date(),
      },
    });

    // Continue processing in background
    pipelinePromise.then(result => {
      logger.info(`Manual pipeline completed: ${result.matchesProcessed} matches, ${result.predictionsGenerated} predictions`);
    }).catch(error => {
      logger.error(`Manual pipeline failed: ${error}`);
    });

  } catch (error) {
    next(error);
  }
};

/**
 * Update automation configuration
 */
export const updateAutomationConfig = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const config = req.body;

    logger.info(`Updating automation configuration by user ${req.user?.email}`);

    await scrapingScheduler.updateConfiguration(config);

    res.json({
      success: true,
      message: 'Automation configuration updated successfully',
      data: {
        config,
        timestamp: new Date(),
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get automation metrics and statistics
 */
export const getAutomationMetrics = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { timeframe = '24h' } = req.query;

    // Calculate time range
    let startDate: Date;
    switch (timeframe) {
      case '1h':
        startDate = new Date(Date.now() - 60 * 60 * 1000);
        break;
      case '24h':
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
        break;
      case '7d':
        startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(Date.now() - 24 * 60 * 60 * 1000);
    }

    // Get metrics from database
    const { prisma } = await import('@/config/database');

    const [scrapingJobs, predictions, auditLogs] = await Promise.all([
      prisma.scrapingJob.findMany({
        where: {
          createdAt: { gte: startDate },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.prediction.count({
        where: {
          createdAt: { gte: startDate },
        },
      }),
      prisma.auditLog.count({
        where: {
          action: 'DATA_PIPELINE_EXECUTED',
          createdAt: { gte: startDate },
        },
      }),
    ]);

    // Calculate metrics
    const totalJobs = scrapingJobs.length;
    const successfulJobs = scrapingJobs.filter((job: any) => job.status === 'COMPLETED').length;
    const failedJobs = scrapingJobs.filter((job: any) => job.status === 'FAILED').length;
    const successRate = totalJobs > 0 ? (successfulJobs / totalJobs) * 100 : 0;

    const totalMatches = scrapingJobs.reduce((sum: number, job: any) => {
      try {
        const results = job.results ? JSON.parse(job.results) : {};
        return sum + (results.totalMatches || 0);
      } catch {
        return sum;
      }
    }, 0);

    res.json({
      success: true,
      data: {
        timeframe,
        metrics: {
          totalJobs,
          successfulJobs,
          failedJobs,
          successRate: Math.round(successRate * 100) / 100,
          totalMatches,
          totalPredictions: predictions,
          pipelineExecutions: auditLogs,
        },
        recentJobs: scrapingJobs.slice(0, 10),
        timestamp: new Date(),
      },
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Test automation system components
 */
export const testAutomation = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { component = 'all' } = req.body;

    logger.info(`Testing automation system (${component}) by user ${req.user?.email}`);

    const results: any = {
      timestamp: new Date(),
      tests: {},
    };

    if (component === 'all' || component === 'scheduler') {
      // Test scheduler
      try {
        const schedulerStatus = scrapingScheduler.getStatus();
        results.tests.scheduler = {
          status: 'passed',
          data: schedulerStatus,
        };
      } catch (error) {
        results.tests.scheduler = {
          status: 'failed',
          error: (error as Error).toString(),
        };
      }
    }

    if (component === 'all' || component === 'pipeline') {
      // Test pipeline
      try {
        const pipelineStatus = await dataPipeline.getStatus();
        results.tests.pipeline = {
          status: 'passed',
          data: pipelineStatus,
        };
      } catch (error) {
        results.tests.pipeline = {
          status: 'failed',
          error: (error as Error).toString(),
        };
      }
    }

    if (component === 'all' || component === 'scraping') {
      // Test scraping (quick test)
      try {
        const { ScrapingManager } = await import('@/services/scraping/scrapingManager');
        const scrapingManager = new ScrapingManager();
        const testResult = await scrapingManager.scrapeProvider('espn', 'football', 'nfl');
        
        results.tests.scraping = {
          status: testResult.success ? 'passed' : 'failed',
          data: {
            matchesFound: testResult.matchesFound,
            source: testResult.source,
          },
          error: testResult.error,
        };
      } catch (error) {
        results.tests.scraping = {
          status: 'failed',
          error: (error as Error).toString(),
        };
      }
    }

    // Determine overall status
    const allTests = Object.values(results.tests) as any[];
    const passedTests = allTests.filter(test => test.status === 'passed').length;
    const totalTests = allTests.length;

    results.overall = {
      status: passedTests === totalTests ? 'passed' : 'partial',
      passedTests,
      totalTests,
    };

    res.json({
      success: true,
      message: 'Automation system test completed',
      data: results,
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get automation logs
 */
export const getAutomationLogs = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const { limit = 50, offset = 0, level = 'all' } = req.query;

    const { prisma } = await import('@/config/database');

    // Get audit logs related to automation
    const logs = await prisma.auditLog.findMany({
      where: {
        action: {
          in: [
            'DATA_PIPELINE_EXECUTED',
            'SCRAPING_CYCLE_ERROR',
            'AUTOMATION_STARTED',
            'AUTOMATION_STOPPED',
          ],
        },
      },
      orderBy: { createdAt: 'desc' },
      take: Number(limit),
      skip: Number(offset),
    });

    res.json({
      success: true,
      data: {
        logs,
        pagination: {
          limit: Number(limit),
          offset: Number(offset),
          total: logs.length,
        },
      },
    });
  } catch (error) {
    next(error);
  }
};
